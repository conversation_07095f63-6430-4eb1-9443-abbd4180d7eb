
define(['jquery', 'Q', 'wizer-api', 'wizletBase'], function ($, Q, WizerApi, WizletBase) {
    
    var MyCode = function () {
        this.type = 'MyCode';
        this.level = 1;
    };

    MyCode.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;
        return WizletBase.loadHandler({ wizlet: this, render: this.render });
    };

    MyCode.prototype.unloadHandler = function () {
        WizletBase.unloadHandler({ wizlet: this });
    };


    MyCode.prototype.render = function (options) {
        
        /** My Code Here */       
        var self = this;
        
        $(document).one('wizer:action:init', function(e, currentAction) { 
            // Remove all localStorage variables used to control the Quiz answers before start
            if (options.wizletInfo.clearAll) {
                localStorage.clear();
            } else {
                if (options.wizletInfo.resetLocalStorage) {
                    $.each(options.wizletInfo.resetLocalStorage, function (idx, local) {
                        localStorage.removeItem(local);
                    });
                }
            }

            if (options.wizletInfo.saveEmail) {
                
                self.wizerApi.addVotes({
                    votes:[{
                        questionId:   self.wizerApi.getQuestionIdByName(options.wizletInfo.saveEmail), 
                        responseText: Wizer.ParticipantEmail
                    }]});
            }

            if (options.wizletInfo.clearModel) {
                
                self.wizerApi.clearModelCache(Wizer.ParticipationId);

                if ( !localStorage.getItem('firstLoad') ) {
                    localStorage['firstLoad'] = true;
                    window.location.reload();
                } else
                    localStorage.removeItem('firstLoad');

            }
            
        });

        return options.wizletInfo.resetVotes ? this.wizerApi.resetVotes() : true;
    };

    

    MyCode.getRegistration = function () {
        return new MyCode();
    };

    return MyCode;

});
