﻿/* 
    "Gaugedisplay2" component that takes a template and some data (db / calc questions or static values) as the input
    and renders the chart using highcharts library.

    TODO : Add support for calcbinder and static data
    Add support for Track Question
    Add support for Filter Questions
    Add unit tests
*/

define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', 'highcharts-styled', 'highcharts-styled-more', 'css!lib/highcharts/code/css/highcharts.css', '../styles/materialize-src/js/materialize.min'], 
        function ($, Q, WizerApi, WizletBase, doT, Highcharts) {

    var Gauges = function () {
        this.type = 'Gauges';
        this.level = 1;
    };

    Gauges.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;

        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];
        
        requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);
        

        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });


        return WizletBase.loadHandler({ wizlet: this, render: this.render });
    };

    Gauges.prototype.unloadHandler = function () {
        //unload wizletbase

        $(document).off("wizer:model:change", this.redrawChart);
        WizletBase.unloadHandler({ wizlet: this });
    };

    Gauges.prototype.render = function (options) {
        var self = this;
        return self.templateDefer.promise.then(function (template) {
            var fragment = template(options.wizletInfo);
            options.context.html(fragment);
            
            //Init Materialize Modal
            if (options.wizletInfo.modal_details) options.context.find('.modal').modal();


            // start rendering chart
            self.renderChart();
            // add redraw binder
            if (options.wizletInfo.dynamic) {
                self.getModelChangedHandler();
                $(document).off('wizer:model:change', self.modelChangedHandler).on("wizer:model:change", { self: self }, self.modelChangedHandler);
            } 
            
            
            //Load a new page into a modal
            options.context.find("[data-modal]").off("click").on("click", function (e) {
                self.loadPage(
                    $(this).data('modal'), 
                    options.context, 
                    self.unsedContext, 
                    $( $( $(this).attr('href')).find('.modal-content') ) 
                );
            });

            
            
            return true;
        })
            .fail(this.wizerApi.showError);
    };

    Gauges.prototype.renderChart = function (options) {
        var self = this;
        var dbQuestions, calcQuestions, questionIDs = [], data = [], trackQuestionId, trackForemanPromise;

        var chartDefaults = {
            chart: {
                type: 'solidgauge',
                backgroundColor: 'transparent',
                width: 150,
                height: 90
            },
			credits: {
				enabled: false
			},
			tooltip: { 
				enabled: false 
			},
            pane: {
                center: ['24%', '50%'],
                size: '120%',
                startAngle: -80,
                endAngle: 80,
                background: {
					borderColor: 'transparent',
                    innerRadius: '85%',
                    outerRadius: '91%',
                    shape: 'arc'
                }
            },
            plotOptions: {
				series: {
					animation: false
				},
                solidgauge: {
                    linecap: "round",
                    rounded: true,
                    stickyTracking: false,
                    innerRadius: '80%',
                    dataLabels: {
						enabled: false,
                        y: 5,
                        borderWidth: 0,
                        useHTML: true,
						format: '{point.y:.0f}',
						style: { fontSize: '9px' }
                    }
                }
            }
        };

        var hmore = require(['highcharts-styled-solid-gauge'], function () {
        //var hmore = require(['content/js-production/lib/highcharts/js/modules/solid-gauge'], function () {
            var idx = -1;
            self.wizletContext.find('.gaugeContainer').each(function (index) {

                idx ++;
                //Skip noGauges chart gauges
                while (self.wizletInfo.gauges[idx] && self.wizletInfo.gauges[idx].noGauge) {
                    idx ++;
                }

                var $el = $(this);
                var gauge = self.wizletInfo.gauges[idx];
                var ispercent = gauge.ispercent;

                var getData;
                
                if (gauge.loadFromModel) {
                    getData = self.getModelVote(gauge.binding);

                } else {
                    getData = self.fetchdata(gauge.binding);
                }
        
                getData.then(function (data) {    
                    chartDefaults.yAxis = gauge.yAxis;
                    chartDefaults.title = gauge.titleGauge.text;
                    chartDefaults.series = [{
                        "name": "Gauge"+index,
                        "data": [Number(data)]
                    }];
                    $el.highcharts(chartDefaults);
                });   


            });
        });
    };

    
    Gauges.prototype.getModelVote = function(question) {
        var self = this;
        var defer = new Q.defer();   

        self.wizerApi.calcBinderCache.getCalcValue(self.wizletInfo.bindings[question].model, question).then(function(value){
            defer.resolve(value); 
        });

        return defer.promise;
        
    }

    Gauges.prototype.fetchdata = function (question) {
        var self = this;
        var fetching = Q.defer();
                
        //The follower listens the model to load the leader's selected option and enable the navigation button
        var questionId = self.wizerApi.getQuestionIdByName(question);
        if (self.wizletInfo.isFollower) {
            self.iAmFollower(self.wizletInfo.isFollower).then(function (follower) {
                if (follower) {
                    var teamId = self.wizerApi.getQuestionIdByName(self.wizletInfo.trackTeam);
                    self.wizerApi.getForemanVotes(teamId,[questionId]).then(function (response) {
                        var data = response.votes[questionId][0];
                        fetching.resolve(data);
                    });

                } else {
                    self.wizerApi.getMyVotes([questionId]).then(function (response) {
                        var data = response.votes[questionId][0];
                        fetching.resolve(data);
                    });
                }
            });

        } else {
            self.wizerApi.getMyVotes([questionId]).then(function (response) {
                var data = response.votes[questionId][0];
                fetching.resolve(data);
            });

        }

        return fetching.promise;
    }

    Gauges.prototype.getModelChangedHandler = function () {

        if (this.modelChangedHandler) {
            return this.modelChangedHandler;
        }
        else {
            return this.modelChangedHandler = function (ev) {
                ev.data.self.reDrawCharts()
            };
        }
    }


    Gauges.prototype.loadPage = function (actionXMLName, context, unusedContext, modalContainer) {
        var self = this;
       
        //unload previous 
        if (self.currentWizletModule && self.currentWizletModule.length > 0) {
            $.each(self.currentWizletModule, function (index, module) {
                if (module.wizletInstance.unloadHandler) {
                    module.wizletInstance.unloadHandler();
                }
            });
        }
        
        var loading = self.wizerApi.loadActionInContainer(actionXMLName, context, unusedContext, modalContainer);
        loading.then(function (loads) {           
            self.currentWizletModule = loads;            
            var page = "wizer:action:init.mainArea";                        
            $(document).trigger(page, actionXMLName);
        });
        
    };


    Gauges.prototype.reDrawCharts = function () {
        var self = this;
        var idx = -1;
        self.wizletContext.find('.gaugeContainer').each(function (index) {
            
            idx ++;
            //Skip noGauges chart gauges
            while (self.wizletInfo.gauges[idx] && self.wizletInfo.gauges[idx].noGauge) {
                idx ++;
            }

            var chart = $(this).highcharts();

            var gauge = self.wizletInfo.gauges[idx];
            var getData;
                
            if (gauge.loadFromModel) {
                getData = self.getModelVote(gauge.binding);

            } else {
                getData = self.fetchdata(gauge.binding);
            }

            getData.then(function (data) { 
                point = chart.series[0].points[0];
                point.update(Number(data));
            });
        });
    }
	

    /**
     * Promise function: check if the participant is a follower
     */
    Gauges.prototype.iAmFollower = function (questionName) {
        var self = this;

        var questionId = self.wizerApi.getQuestionIdByName(questionName);

        var defer = Q.defer();

        self.wizerApi.getMyVotes([questionId]).then(function (result) {     
            defer.resolve( result.votes[questionId][0] == 1 );
        });                    
        
        return defer.promise;
    };

	Gauges.getRegistration = function () {
        return new Gauges();
    };

    return Gauges;
});
