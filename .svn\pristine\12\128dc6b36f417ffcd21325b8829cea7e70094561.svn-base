
<!-- Tabs header -->
{{=it.components[0]}}
    
<!-- Tabs panels (x2 components per each tab) -->
{{var cont=0; for (var idx = 1; idx < it.components.length; idx += 2) { }}
{{?it.components[idx+1] }}
    <div id="tabPanelModal{{=cont}}" class="col s12 tab-component">
        {{=it.components[idx]}}
        {{=it.components[idx+1]}}
    </div>
{{??}}
    {{=it.components[idx]}}
{{?}}
{{ cont++}; }}  


