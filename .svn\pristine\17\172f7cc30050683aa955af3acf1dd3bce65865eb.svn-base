﻿<div class="container{{?it.animate}} animate__animated animate__{{=it.animate}}{{?}}{{?it.isHiddenWhenSmall}} hide-on-med-and-down{{?}}{{?it.isHiddenWhenBig}} hide-on-large-only{{?}}"
{{?it.id}}id="{{=it.id}}"{{?}} {{?it.isHidden}}hidden{{?}}> 
    
    {{?it.header}}<h4 class="header {{?it.header_color}}client-colors-text text-{{=it.header_color}}{{?}}">{{=it.header}}</h4>{{?}}
    {{?it.subheader}}<h5 class="header {{?it.header_color}}client-colors-text text-{{=it.header_color}}{{?}}">{{=it.subheader}}</h5>{{?}}
    {{?it.instructions}}<h6 class="instructions flow-text">{{=it.instructions}}</h6>{{?}}

    <div class="card hoverable {{?it.size}}{{=it.size}}{{?}}">

        {{?it.content && (it.content.title || it.content.body)}}
        <div class="card-content no-pad-bot">
            {{?it.content.title}}<span class="card-title">{{=it.content.title}}</span>{{?}}
            {{?it.content.body}}<p class="flow-text">{{=it.content.body}}</p>{{?}}
        </div>  
        {{?}}
        
        {{?it.image}}
        <div class="card-image">
            <img class="responsive-img" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.image.src}}" alt="{{=it.image.alt}}"/>
        </div>  
        {{?}}


        <ul class="collapsible {{?it.popout}}popout{{?}} {{? !it.accordion}}expandable{{?}}">   
            {{~it.items :item:idx}}
                <li data-index="{{=idx+1}}"
                    {{?it.bind && it.DB[it.bind]}} 
                        {{?it.DB[it.bind]==(idx+1)}}class="active"{{?}}>
                        
                        <div class="collapsible-header flow-text">
                            <i class="expand material-icons large bottom">{{?it.DB[it.bind]==(idx+1)}}{{=it.expand_less}}{{??}}{{=it.expand_more}}{{?}}</i>

                    {{??}}
                        {{?item.active}}class="active"{{?}}>
                        
                        <div class="collapsible-header flow-text">
                            <i class="expand material-icons large bottom">{{?item.active}}{{=it.expand_less}}{{??}}{{=it.expand_more}}{{?}}</i>
                    {{?}}

                        <span class="strong">{{=item.title}}</span>
                    </div>
                    <div class="collapsible-body flow-text">
                        {{?item.text}}<span>{{=item.text}}</span>{{?}}
                        {{?item.texts}}
                            {{~item.texts :subitem:idx2}}
                                {{?subitem.title}}<span class="strong">{{=subitem.title}}</span>{{?}}
                                {{?subitem.text}}<br><span>{{=subitem.text}}</span>{{?}}
                                {{?idx2 < item.texts.length-1}}</br><br>{{?}}
                            {{~}}
                        {{?}}
                    </div>
                </li>
            {{~}}
        </ul>
                    
    </div>

</div>

