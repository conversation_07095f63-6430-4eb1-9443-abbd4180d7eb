<?xml version="1.0" encoding="utf-8" ?>
<Action autoNext="true">

  <!-- ************************************************************ -->
  <!-- *********************** AGGREGATION  *********************** -->
  <!-- ************************************************************ -->

  <Aggregator doneText="">
    <ParticipantFilter>
        <Question>Filter_Agg</Question>
    </ParticipantFilter>

    <!-- KPI 1 -->
    
    <!--KPI 1 global-->
    <Score result="Score_SIM_R1_Scenario7_KPI1" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario7">
          <Choice value="1" Response="!{SIM_R1_Scenario7_Opt1_KPI1}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario7_Opt2_KPI1}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario7_Opt3_KPI1}"></Choice>
      </Question>
    </Score> 
    <Total result="Score_SIM_Total_KPI1_R1" method="sum">
      <Question validate="false">Score_SIM_Init_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Initiatives_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario5_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario6_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario7_KPI1</Question>
    </Total>
    
    <!--KPI2 global-->
    <Score result="Score_SIM_R1_Scenario7_KPI2" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario7">
          <Choice value="1" Response="!{SIM_R1_Scenario7_Opt1_KPI2}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario7_Opt2_KPI2}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario7_Opt3_KPI2}"></Choice>
      </Question>
    </Score> 
    <Total result="Score_SIM_Total_KPI2_R1" method="sum">
      <Question validate="false">Score_SIM_Init_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Initiatives_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario5_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario6_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario7_KPI2</Question>
    </Total>
    
    <!--KPI3 global-->
    <Score result="Score_SIM_R1_Scenario7_KPI3" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario7">
          <Choice value="1" Response="!{SIM_R1_Scenario7_Opt1_KPI3}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario7_Opt2_KPI3}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario7_Opt3_KPI3}"></Choice>
      </Question>
    </Score> 
    <Total result="Score_SIM_Total_KPI3_R1" method="sum">
      <Question validate="false">Score_SIM_Init_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Initiatives_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario5_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario6_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario7_KPI3</Question>
    </Total>

    <!-- MTUs -->
    <!-- R1 -->
    <Score result="Score_SIM_R1_Scenario7_KPI4" type="Choice"> 
      <Question name="Q_SIM_R1_Scenario7">
          <Choice value="1" Response="!{SIM_R1_Scenario7_Opt1_KPI4}"></Choice>
          <Choice value="2" Response="!{SIM_R1_Scenario7_Opt2_KPI4}"></Choice>
          <Choice value="3" Response="!{SIM_R1_Scenario7_Opt3_KPI4}"></Choice>
      </Question>
    </Score> 
    <Total result="Score_SIM_Total_KPI4_R1" method="sum">
      <Question validate="false">Score_SIM_Init_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Initiatives_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario5_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario6_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario7_KPI4</Question>
    </Total>

    <!-- KPI: TOTAL -->
    <Total result="Score_SIM_Total_R1" method="avg">
      <Question validate="false">Score_SIM_Total_KPI1_R1</Question>
      <Question validate="false">Score_SIM_Total_KPI2_R1</Question>
      <Question validate="false">Score_SIM_Total_KPI3_R1</Question>
    </Total>

    <Total result="Score_SIM_Total_R1_Rank" method="samerank">
        <Question validate="false">Score_SIM_Total_R1</Question>
    </Total>
    
  </Aggregator>

</Action>