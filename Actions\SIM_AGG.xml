<?xml version="1.0" encoding="utf-8" ?>
<Action>

  <!-- ************************************************************ -->
  <!-- *********************** AGGREGATION  *********************** -->
  <!-- ************************************************************ -->

  <Aggregator doneText="">
    <ParticipantFilter>
        <Question>Filter_Agg</Question>
    </ParticipantFilter>

    <!-- KPI1: -->

    <!-- R1 -->

    <Total result="Score_SIM_Total_KPI1_R1" method="sum">
      <Question validate="false">Score_SIM_Init_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Initiatives_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario5_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario6_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario7_KPI1</Question>
    </Total>

    
    <!-- KPI2 -->
        
    <!-- R1 -->

    <Total result="Score_SIM_Total_KPI2_R1" method="sum">
      <Question validate="false">Score_SIM_Init_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Initiatives_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario5_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario6_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario7_KPI2</Question>
    </Total>    

    
    <!-- KPI3 -->
      
    <!-- R1 -->

    <Total result="Score_SIM_Total_KPI3_R1" method="sum">
      <Question validate="false">Score_SIM_Init_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Initiatives_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario5_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario6_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario7_KPI3</Question>
    </Total>

    

    <!-- MTUs -->
    <!-- R1 -->
    <Total result="Score_SIM_Total_KPI4_R1" method="sum">
      <Question validate="false">Score_SIM_Init_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Initiatives_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario3_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario4_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario5_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario6_KPI4</Question>
      <Question validate="false">Score_SIM_R1_Scenario7_KPI4</Question>
    </Total>

    

    <!-- Totales -->

    <Total result="Score_SIM_Total_R1" method="avg">
      <Question validate="false">Score_SIM_Total_KPI1_R1</Question>
      <Question validate="false">Score_SIM_Total_KPI2_R1</Question>
      <Question validate="false">Score_SIM_Total_KPI3_R1</Question>
    </Total>

    
    <Total result="Score_SIM_Total_R1_Rank" method="samerank">
        <Question validate="false">Score_SIM_Total_R1</Question>
    </Total>
    

  </Aggregator>


</Action>