
<!-- Tabs header -->
{{=it.components[0]}}
    
<!-- Tabs panels (x3 components per each tab) -->
{{var cont=0; for (var idx = 1; idx < it.components.length; idx += 3) { }}
{{?it.components[idx+1] && it.components[idx+2]}}
    <div id="tabPanel{{=cont}}" class="col s12 tab-component">
        {{=it.components[idx]}}
        {{=it.components[idx+1]}}
        {{=it.components[idx+2]}}
    </div>
{{??}}
    {{?it.components[idx]}} {{=it.components[idx]}} {{?}}
    {{?it.components[idx+1]}} {{=it.components[idx+1]}} {{?}}
{{?}}
{{ cont++}; }}  

