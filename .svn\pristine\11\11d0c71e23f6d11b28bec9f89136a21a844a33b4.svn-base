﻿/* 
    "HCBarChart" component that takes a template and some data as the input
    and renders the chart using highcharts library.

*/

define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', 'highcharts-styled', 'css!lib/highcharts/code/css/highcharts.css', 'numeral', 'jsCalcLib/numberFormatting'], 
        function ($, Q, WizerApi, WizletBase, doT, Highcharts, HighchartsCss, numeral, numberFormatting) {

    var HCBarChartModel = function () {
        this.type = 'HCBarChartModel';
        this.level = 1;
        
        this.chartDefaults = {
            "lang": {
                "thousandsSep": ",",
                "numericSymbols": ['k', 'm', 'b', 't']
            },
            "chart": {
                "type": 'bar'
            },
            "credits": {
                "enabled": false
            },
            "legend":{ },
            "title": {
                "text": ''
            },
           "xAxis": [
                {
                    "categories": []
                }
			],
            "yAxis": {
                "stackLabels": {
                    "enabled": false
                },
                "labels": {
                }
            },
            "tooltip": {
                "headerFormat": '<b>{point.x}</b><br/>',
                // "pointFormat": '{series.name}: {point.y}<br/>Total: {point.stackTotal}'
                "pointFormat": '{series.name}: {point.y}'
            },
            "plotOptions": {
                "column": {
                    "dataLabels": {
                        "enabled": true
                    }
                }
            }
        }

    };

    HCBarChartModel.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;
        

        this.templateDefer = new Q.defer();
        var self = this;
        var requirements = [];
        requirements.push(WizletBase.loadTemplate(wizletInfo, 'hCBarChartModel.dot'));

        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });
    };

    HCBarChartModel.prototype.unloadHandler = function () {
        //unload wizletbase
        WizletBase.unloadHandler({ wizlet: this });
        $(document).off("wizer:model:change", this.redrawChart);
    };

    HCBarChartModel.prototype.render = function (options) {
        var self = this;
		 var fetching = new Q.defer();
        return self.templateDefer.promise.then(function (template) {
            var fragment = template(options.wizletInfo);
            options.context.html(fragment);
			self.isDelivery = false;
            
            if (!self.wizletInfo.id) self.wizletInfo.id = 'barChart0';
            
            
            // start rendering chart
            self.renderChart().then(function () {
                fetching.resolve(true);    
                $(document).off("wizer:model:change."+self.wizletInfo.id, self.redrawChart).
                            on("wizer:model:change."+self.wizletInfo.id, {self: self}, self.redrawChart);            
            });
        })
        .fail(this.wizerApi.showError);
    };

    HCBarChartModel.prototype.renderChart = function() {
        var self = this;
        var rendering = new Q.defer();
        var chartOptions = self.wizletInfo.chartConfig;
        var chartElem = self.wizletContext.find("[data-stackedcolumnchartholder]");

        chartOptions = $.extend(true, {}, self.chartDefaults, chartOptions);


        if (chartOptions.yAxis.myLabels) {
            var change = chartOptions.yAxis.myLabels;
            chartOptions.yAxis.labels.formatter = function() {
                var value = change[this.value];
                return value !== 'undefined' ? value : this.value;                
            }
        }
        
        // check for db questions
        if (typeof self.wizletInfo.questions !== "undefined" && self.wizletInfo.questions.length) {
            
            //self.fetchVotes().then(function(data){
            self.getModelVotes().then(function(votes){
                self.votes = votes;
                var data = self.createSeries(votes);
                self.renderChartData (data, chartOptions, chartElem);
                rendering.resolve(true);
            });
        }
        else {
            //if no questions, direct values given in the XML by the series
            chartElem.highcharts(chartOptions);
            rendering.resolve(true);
        }

		return rendering.promise;
    };

    
    HCBarChartModel.prototype.redrawChart = function(e) {
        var self = e.data.self;
        var redraw = new Q.defer();
        var chartOptions = self.wizletInfo.chartConfig;
        var chartElem = self.wizletContext.find("[data-stackedcolumnchartholder]");
        chartOptions = $.extend(true, {}, self.chartDefaults, chartOptions);

        
        // check for db questions
        if (typeof self.wizletInfo.questions !== "undefined" && self.wizletInfo.questions.length) {
            
            self.getModelVotes().then(function(votes){
                
                if (self.votesUpdated(votes) == true) {
                    var data = self.createSeries(votes);
                    self.renderChartData (data, chartOptions, chartElem);
                }
                redraw.resolve(true);
            });
        }
        else {
            redraw.resolve(true);
        }
        return redraw.promise;
    };



    HCBarChartModel.prototype.getModelVotes = function() {
        var self = this;

        var defer = new Q.defer();   

        var questions = self.wizletInfo.questions;
        var values = [];
        var counter = 1;
     
        questions.forEach(function(question, idx) {

            self.wizerApi.calcBinderCache.getCalcValue(question.model, question.bind).then(function(value){
                values[idx] = value;
                if (counter == questions.length) {
                    defer.resolve(values); //when all model values loaded
                } else {
                    counter ++;
                }
            });
        });

        return defer.promise;
        
    }

    
    
    HCBarChartModel.prototype.votesUpdated = function(values) {

        var self=this, isUpdated=false;

        self.votes.forEach(function(value, idx) {
            if (value !== values[idx]) {
                isUpdated = true;
            }
        })          
        if (isUpdated) self.votes = values;
        
        return isUpdated;
    };


    HCBarChartModel.prototype.createSeries = function(values) {

        var self = this, retVal = {}, series = [];
        
        self.wizletInfo.chartConfig.series.forEach(function(serie, idx) {
            // series.push({name: serie, data: values});
            if (serie.values) {
                series.push({data: values.slice(idx*serie.values,(idx+1)*serie.values)});
            } else {
                series.push({data: values});
            }
        })
                
        retVal.series = series;
        retVal.xAxis = self.wizletInfo.chartConfig.xAxis;
        
        return retVal;

    };
    


    HCBarChartModel.prototype.renderChartData = function(data, chartOptions, chartElem) {

        chartOptions.xAxis = $.extend(true, [], chartOptions.xAxis, data.xAxis);
        if (chartOptions.solutionLine) data.series.push(chartOptions.solutionLine);
        chartOptions.series = $.extend(true, [], chartOptions.series, data.series);
        
        if (chartOptions.yAxis.myLabels) {
            var change = chartOptions.yAxis.myLabels;
            chartOptions.yAxis.labels.formatter = function() {
                var value = change[this.value];
                return value !== 'undefined' ? value : this.value;                
            }
        } else {
            this.applyNumberFormat(chartOptions)
        }

        this.formatTooltips(chartOptions);
        
        chartElem.highcharts(chartOptions);	
    };

    HCBarChartModel.prototype.formatTooltips = function(chartOptions) {
        var formatters = discover(chartOptions, 'numformat');

        formatters.forEach(function(ob) {
            var format = ob.numformat,
                scaler = ob.scaler,
                formatFunction = (function(){
                    return function() {
                        var val = 0, point = this, out = '<strong>' + point.series.name + '</strong><br />';
                        out += '' + point.x + ': ';
                        if (typeof this.value !== "undefined") {
                            val = this.value;
                        }
                        else if (typeof this.y !== "undefined") {
                            val = this.y;
                        }
                        else if (typeof this.x !== "undefined") {
                            val = this.x;
                        }
                        out += numberFormatting.format(val, format, scaler);
                        return out;
                    }
                }());

            ob.formatter = formatFunction;
        });
    }

    HCBarChartModel.prototype.applyNumberFormat = function(chartOptions) {
        var formatters = discover(chartOptions, 'formatter');

        formatters.forEach(function(ob) {
            var format = ob.formatter,
                scaler = ob.scaler,
                formatFunction = (function(){
                    return function() {
                        var val = 0;
                        if (typeof this.value !== "undefined") {
                            val = this.value;
                        }
                        else if (typeof this.y !== "undefined") {
                            val = this.y;
                        }
                        else if (typeof this.x !== "undefined") {
                            val = this.x;
                        }
                        return numberFormatting.format(val, format, scaler);
                    }
                }());

            ob.formatter = formatFunction;
        });
    }

    HCBarChartModel.getRegistration = function () {
        return new HCBarChartModel();
    };


    function search(tree, propertyName, result) {
        if ($.isArray(tree)) {
            for (var i = 0; i < tree.length; ++i) {
                search(tree[i], propertyName, result);
            }
        } else if ($.isPlainObject(tree)) {
            for (var pName in tree) {
                if (tree.hasOwnProperty(pName)) {
                    var subTree = tree[pName];
                    if (pName == propertyName) {
                        result.push(tree);
                    } else {
                        search(subTree, propertyName, result);
                    }
                }
            }
        }
    };

    function discover(src, propertyName) { 
        var propertyName = propertyName || 'formatter';
        var formatters = [];
        search(src, propertyName, formatters);
        return formatters;
    };

	
	
    return HCBarChartModel;

});
