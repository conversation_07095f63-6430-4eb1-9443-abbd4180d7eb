.confetti-wrapper {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.confetti-wrapper [class|=confetti] {
  position: absolute;
}
.confetti-wrapper .confetti-0 {
  width: 6px;
  height: 2.4px;
  background-color: #3f51b5;
  top: -10%;
  left: 55%;
  opacity: 1.3882085406;
  -webkit-transform: rotate(339.0955656766deg);
      -ms-transform: rotate(339.0955656766deg);
       -o-transform: rotate(339.0955656766deg);
          transform: rotate(339.0955656766deg);
  -webkit-animation: drop-0 4.2380818346s 0.9977157352s infinite;
       -o-animation: drop-0 4.2380818346s 0.9977157352s infinite;
          animation: drop-0 4.2380818346s 0.9977157352s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-0 {
  100% {
    top: 110%;
    left: 57%;
  }
}
@-o-keyframes drop-0 {
  100% {
    top: 110%;
    left: 57%;
  }
}
@keyframes drop-0 {
  100% {
    top: 110%;
    left: 57%;
  }
}
.confetti-wrapper .confetti-1 {
  width: 1px;
  height: 0.4px;
  background-color: #03a9f4;
  top: -10%;
  left: 83%;
  opacity: 1.1596780401;
  -webkit-transform: rotate(202.7784931933deg);
      -ms-transform: rotate(202.7784931933deg);
       -o-transform: rotate(202.7784931933deg);
          transform: rotate(202.7784931933deg);
  -webkit-animation: drop-1 4.8580573664s 0.5704430163s infinite;
       -o-animation: drop-1 4.8580573664s 0.5704430163s infinite;
          animation: drop-1 4.8580573664s 0.5704430163s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-1 {
  100% {
    top: 110%;
    left: 92%;
  }
}
@-o-keyframes drop-1 {
  100% {
    top: 110%;
    left: 92%;
  }
}
@keyframes drop-1 {
  100% {
    top: 110%;
    left: 92%;
  }
}
.confetti-wrapper .confetti-2 {
  width: 7px;
  height: 2.8px;
  background-color: #9c27b0;
  top: -10%;
  left: 58%;
  opacity: 1.0491819406;
  -webkit-transform: rotate(189.1104617083deg);
      -ms-transform: rotate(189.1104617083deg);
       -o-transform: rotate(189.1104617083deg);
          transform: rotate(189.1104617083deg);
  -webkit-animation: drop-2 4.455847799s 0.9817334458s infinite;
       -o-animation: drop-2 4.455847799s 0.9817334458s infinite;
          animation: drop-2 4.455847799s 0.9817334458s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-2 {
  100% {
    top: 110%;
    left: 59%;
  }
}
@-o-keyframes drop-2 {
  100% {
    top: 110%;
    left: 59%;
  }
}
@keyframes drop-2 {
  100% {
    top: 110%;
    left: 59%;
  }
}
.confetti-wrapper .confetti-3 {
  width: 8px;
  height: 3.2px;
  background-color: #ff5722;
  top: -10%;
  left: 25%;
  opacity: 1.4198788091;
  -webkit-transform: rotate(147.3900221052deg);
      -ms-transform: rotate(147.3900221052deg);
       -o-transform: rotate(147.3900221052deg);
          transform: rotate(147.3900221052deg);
  -webkit-animation: drop-3 4.3893037886s 0.9663451891s infinite;
       -o-animation: drop-3 4.3893037886s 0.9663451891s infinite;
          animation: drop-3 4.3893037886s 0.9663451891s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-3 {
  100% {
    top: 110%;
    left: 33%;
  }
}
@-o-keyframes drop-3 {
  100% {
    top: 110%;
    left: 33%;
  }
}
@keyframes drop-3 {
  100% {
    top: 110%;
    left: 33%;
  }
}
.confetti-wrapper .confetti-4 {
  width: 9px;
  height: 3.6px;
  background-color: #009688;
  top: -10%;
  left: 2%;
  opacity: 1.4266643948;
  -webkit-transform: rotate(284.4449466379deg);
      -ms-transform: rotate(284.4449466379deg);
       -o-transform: rotate(284.4449466379deg);
          transform: rotate(284.4449466379deg);
  -webkit-animation: drop-4 4.3694375129s 0.1310709367s infinite;
       -o-animation: drop-4 4.3694375129s 0.1310709367s infinite;
          animation: drop-4 4.3694375129s 0.1310709367s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-4 {
  100% {
    top: 110%;
    left: 10%;
  }
}
@-o-keyframes drop-4 {
  100% {
    top: 110%;
    left: 10%;
  }
}
@keyframes drop-4 {
  100% {
    top: 110%;
    left: 10%;
  }
}
.confetti-wrapper .confetti-5 {
  width: 4px;
  height: 1.6px;
  background-color: #4CAF50;
  top: -10%;
  left: 40%;
  opacity: 0.7226556125;
  -webkit-transform: rotate(161.8416960963deg);
      -ms-transform: rotate(161.8416960963deg);
       -o-transform: rotate(161.8416960963deg);
          transform: rotate(161.8416960963deg);
  -webkit-animation: drop-5 4.1358035101s 0.8176849487s infinite;
       -o-animation: drop-5 4.1358035101s 0.8176849487s infinite;
          animation: drop-5 4.1358035101s 0.8176849487s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-5 {
  100% {
    top: 110%;
    left: 41%;
  }
}
@-o-keyframes drop-5 {
  100% {
    top: 110%;
    left: 41%;
  }
}
@keyframes drop-5 {
  100% {
    top: 110%;
    left: 41%;
  }
}
.confetti-wrapper .confetti-6 {
  width: 3px;
  height: 1.2px;
  background-color: #C1BB00;
  top: -10%;
  left: 26%;
  opacity: 1.4518921576;
  -webkit-transform: rotate(238.4959699835deg);
      -ms-transform: rotate(238.4959699835deg);
       -o-transform: rotate(238.4959699835deg);
          transform: rotate(238.4959699835deg);
  -webkit-animation: drop-6 4.8072431779s 0.4370724004s infinite;
       -o-animation: drop-6 4.8072431779s 0.4370724004s infinite;
          animation: drop-6 4.8072431779s 0.4370724004s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-6 {
  100% {
    top: 110%;
    left: 35%;
  }
}
@-o-keyframes drop-6 {
  100% {
    top: 110%;
    left: 35%;
  }
}
@keyframes drop-6 {
  100% {
    top: 110%;
    left: 35%;
  }
}
.confetti-wrapper .confetti-7 {
  width: 6px;
  height: 2.4px;
  background-color: #e91e63;
  top: -10%;
  left: 69%;
  opacity: 1.4126875255;
  -webkit-transform: rotate(51.4493577761deg);
      -ms-transform: rotate(51.4493577761deg);
       -o-transform: rotate(51.4493577761deg);
          transform: rotate(51.4493577761deg);
  -webkit-animation: drop-7 4.6581430907s 0.5362714555s infinite;
       -o-animation: drop-7 4.6581430907s 0.5362714555s infinite;
          animation: drop-7 4.6581430907s 0.5362714555s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-7 {
  100% {
    top: 110%;
    left: 83%;
  }
}
@-o-keyframes drop-7 {
  100% {
    top: 110%;
    left: 83%;
  }
}
@keyframes drop-7 {
  100% {
    top: 110%;
    left: 83%;
  }
}
.confetti-wrapper .confetti-8 {
  width: 6px;
  height: 2.4px;
  background-color: #F44336;
  top: -10%;
  left: 47%;
  opacity: 1.160573534;
  -webkit-transform: rotate(334.676697463deg);
      -ms-transform: rotate(334.676697463deg);
       -o-transform: rotate(334.676697463deg);
          transform: rotate(334.676697463deg);
  -webkit-animation: drop-8 4.5177608432s 0.2536012128s infinite;
       -o-animation: drop-8 4.5177608432s 0.2536012128s infinite;
          animation: drop-8 4.5177608432s 0.2536012128s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-8 {
  100% {
    top: 110%;
    left: 51%;
  }
}
@-o-keyframes drop-8 {
  100% {
    top: 110%;
    left: 51%;
  }
}
@keyframes drop-8 {
  100% {
    top: 110%;
    left: 51%;
  }
}
.confetti-wrapper .confetti-9 {
  width: 4px;
  height: 1.6px;
  background-color: #009688;
  top: -10%;
  left: 24%;
  opacity: 1.2144150805;
  -webkit-transform: rotate(211.2957426896deg);
      -ms-transform: rotate(211.2957426896deg);
       -o-transform: rotate(211.2957426896deg);
          transform: rotate(211.2957426896deg);
  -webkit-animation: drop-9 4.8302061473s 0.4803808305s infinite;
       -o-animation: drop-9 4.8302061473s 0.4803808305s infinite;
          animation: drop-9 4.8302061473s 0.4803808305s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-9 {
  100% {
    top: 110%;
    left: 27%;
  }
}
@-o-keyframes drop-9 {
  100% {
    top: 110%;
    left: 27%;
  }
}
@keyframes drop-9 {
  100% {
    top: 110%;
    left: 27%;
  }
}
.confetti-wrapper .confetti-10 {
  width: 5px;
  height: 2px;
  background-color: #673ab7;
  top: -10%;
  left: 16%;
  opacity: 0.8703882711;
  -webkit-transform: rotate(218.3588932074deg);
      -ms-transform: rotate(218.3588932074deg);
       -o-transform: rotate(218.3588932074deg);
          transform: rotate(218.3588932074deg);
  -webkit-animation: drop-10 4.3508736623s 0.3818468616s infinite;
       -o-animation: drop-10 4.3508736623s 0.3818468616s infinite;
          animation: drop-10 4.3508736623s 0.3818468616s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-10 {
  100% {
    top: 110%;
    left: 30%;
  }
}
@-o-keyframes drop-10 {
  100% {
    top: 110%;
    left: 30%;
  }
}
@keyframes drop-10 {
  100% {
    top: 110%;
    left: 30%;
  }
}
.confetti-wrapper .confetti-11 {
  width: 4px;
  height: 1.6px;
  background-color: #ff9800;
  top: -10%;
  left: 20%;
  opacity: 0.5808477021;
  -webkit-transform: rotate(349.3994827862deg);
      -ms-transform: rotate(349.3994827862deg);
       -o-transform: rotate(349.3994827862deg);
          transform: rotate(349.3994827862deg);
  -webkit-animation: drop-11 4.2215454376s 0.6261620971s infinite;
       -o-animation: drop-11 4.2215454376s 0.6261620971s infinite;
          animation: drop-11 4.2215454376s 0.6261620971s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-11 {
  100% {
    top: 110%;
    left: 33%;
  }
}
@-o-keyframes drop-11 {
  100% {
    top: 110%;
    left: 33%;
  }
}
@keyframes drop-11 {
  100% {
    top: 110%;
    left: 33%;
  }
}
.confetti-wrapper .confetti-12 {
  width: 10px;
  height: 4px;
  background-color: #ff9800;
  top: -10%;
  left: 77%;
  opacity: 1.4473871055;
  -webkit-transform: rotate(167.0907376251deg);
      -ms-transform: rotate(167.0907376251deg);
       -o-transform: rotate(167.0907376251deg);
          transform: rotate(167.0907376251deg);
  -webkit-animation: drop-12 4.0644604661s 0.3613645112s infinite;
       -o-animation: drop-12 4.0644604661s 0.3613645112s infinite;
          animation: drop-12 4.0644604661s 0.3613645112s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-12 {
  100% {
    top: 110%;
    left: 80%;
  }
}
@-o-keyframes drop-12 {
  100% {
    top: 110%;
    left: 80%;
  }
}
@keyframes drop-12 {
  100% {
    top: 110%;
    left: 80%;
  }
}
.confetti-wrapper .confetti-13 {
  width: 10px;
  height: 4px;
  background-color: #8bc34a;
  top: -10%;
  left: 64%;
  opacity: 0.9405911304;
  -webkit-transform: rotate(250.2417482899deg);
      -ms-transform: rotate(250.2417482899deg);
       -o-transform: rotate(250.2417482899deg);
          transform: rotate(250.2417482899deg);
  -webkit-animation: drop-13 4.1563550128s 0.7205521024s infinite;
       -o-animation: drop-13 4.1563550128s 0.7205521024s infinite;
          animation: drop-13 4.1563550128s 0.7205521024s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-13 {
  100% {
    top: 110%;
    left: 70%;
  }
}
@-o-keyframes drop-13 {
  100% {
    top: 110%;
    left: 70%;
  }
}
@keyframes drop-13 {
  100% {
    top: 110%;
    left: 70%;
  }
}
.confetti-wrapper .confetti-14 {
  width: 2px;
  height: 0.8px;
  background-color: #4CAF50;
  top: -10%;
  left: 5%;
  opacity: 0.9030205487;
  -webkit-transform: rotate(308.7945361885deg);
      -ms-transform: rotate(308.7945361885deg);
       -o-transform: rotate(308.7945361885deg);
          transform: rotate(308.7945361885deg);
  -webkit-animation: drop-14 4.7026418519s 0.2238830706s infinite;
       -o-animation: drop-14 4.7026418519s 0.2238830706s infinite;
          animation: drop-14 4.7026418519s 0.2238830706s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-14 {
  100% {
    top: 110%;
    left: 10%;
  }
}
@-o-keyframes drop-14 {
  100% {
    top: 110%;
    left: 10%;
  }
}
@keyframes drop-14 {
  100% {
    top: 110%;
    left: 10%;
  }
}
.confetti-wrapper .confetti-15 {
  width: 4px;
  height: 1.6px;
  background-color: #cddc39;
  top: -10%;
  left: 37%;
  opacity: 1.210828186;
  -webkit-transform: rotate(266.2560112601deg);
      -ms-transform: rotate(266.2560112601deg);
       -o-transform: rotate(266.2560112601deg);
          transform: rotate(266.2560112601deg);
  -webkit-animation: drop-15 4.9850950188s 0.515967461s infinite;
       -o-animation: drop-15 4.9850950188s 0.515967461s infinite;
          animation: drop-15 4.9850950188s 0.515967461s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-15 {
  100% {
    top: 110%;
    left: 41%;
  }
}
@-o-keyframes drop-15 {
  100% {
    top: 110%;
    left: 41%;
  }
}
@keyframes drop-15 {
  100% {
    top: 110%;
    left: 41%;
  }
}
.confetti-wrapper .confetti-16 {
  width: 2px;
  height: 0.8px;
  background-color: #673ab7;
  top: -10%;
  left: 43%;
  opacity: 1.3906910984;
  -webkit-transform: rotate(304.2286790329deg);
      -ms-transform: rotate(304.2286790329deg);
       -o-transform: rotate(304.2286790329deg);
          transform: rotate(304.2286790329deg);
  -webkit-animation: drop-16 4.684259841s 0.7410241118s infinite;
       -o-animation: drop-16 4.684259841s 0.7410241118s infinite;
          animation: drop-16 4.684259841s 0.7410241118s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-16 {
  100% {
    top: 110%;
    left: 52%;
  }
}
@-o-keyframes drop-16 {
  100% {
    top: 110%;
    left: 52%;
  }
}
@keyframes drop-16 {
  100% {
    top: 110%;
    left: 52%;
  }
}
.confetti-wrapper .confetti-17 {
  width: 8px;
  height: 3.2px;
  background-color: #03a9f4;
  top: -10%;
  left: 8%;
  opacity: 0.5765503102;
  -webkit-transform: rotate(359.8257869168deg);
      -ms-transform: rotate(359.8257869168deg);
       -o-transform: rotate(359.8257869168deg);
          transform: rotate(359.8257869168deg);
  -webkit-animation: drop-17 4.7400238891s 0.1334319565s infinite;
       -o-animation: drop-17 4.7400238891s 0.1334319565s infinite;
          animation: drop-17 4.7400238891s 0.1334319565s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-17 {
  100% {
    top: 110%;
    left: 16%;
  }
}
@-o-keyframes drop-17 {
  100% {
    top: 110%;
    left: 16%;
  }
}
@keyframes drop-17 {
  100% {
    top: 110%;
    left: 16%;
  }
}
.confetti-wrapper .confetti-18 {
  width: 10px;
  height: 4px;
  background-color: #F44336;
  top: -10%;
  left: 96%;
  opacity: 0.585791308;
  -webkit-transform: rotate(88.9697799655deg);
      -ms-transform: rotate(88.9697799655deg);
       -o-transform: rotate(88.9697799655deg);
          transform: rotate(88.9697799655deg);
  -webkit-animation: drop-18 4.2308079086s 0.6702240332s infinite;
       -o-animation: drop-18 4.2308079086s 0.6702240332s infinite;
          animation: drop-18 4.2308079086s 0.6702240332s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-18 {
  100% {
    top: 110%;
    left: 110%;
  }
}
@-o-keyframes drop-18 {
  100% {
    top: 110%;
    left: 110%;
  }
}
@keyframes drop-18 {
  100% {
    top: 110%;
    left: 110%;
  }
}
.confetti-wrapper .confetti-19 {
  width: 7px;
  height: 2.8px;
  background-color: #ff9800;
  top: -10%;
  left: 34%;
  opacity: 0.7947329867;
  -webkit-transform: rotate(326.0372376108deg);
      -ms-transform: rotate(326.0372376108deg);
       -o-transform: rotate(326.0372376108deg);
          transform: rotate(326.0372376108deg);
  -webkit-animation: drop-19 4.4616118495s 0.7930031246s infinite;
       -o-animation: drop-19 4.4616118495s 0.7930031246s infinite;
          animation: drop-19 4.4616118495s 0.7930031246s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-19 {
  100% {
    top: 110%;
    left: 44%;
  }
}
@-o-keyframes drop-19 {
  100% {
    top: 110%;
    left: 44%;
  }
}
@keyframes drop-19 {
  100% {
    top: 110%;
    left: 44%;
  }
}
.confetti-wrapper .confetti-20 {
  width: 2px;
  height: 0.8px;
  background-color: #F44336;
  top: -10%;
  left: 19%;
  opacity: 0.9119178526;
  -webkit-transform: rotate(218.3120714659deg);
      -ms-transform: rotate(218.3120714659deg);
       -o-transform: rotate(218.3120714659deg);
          transform: rotate(218.3120714659deg);
  -webkit-animation: drop-20 4.8327374642s 0.4691630854s infinite;
       -o-animation: drop-20 4.8327374642s 0.4691630854s infinite;
          animation: drop-20 4.8327374642s 0.4691630854s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-20 {
  100% {
    top: 110%;
    left: 25%;
  }
}
@-o-keyframes drop-20 {
  100% {
    top: 110%;
    left: 25%;
  }
}
@keyframes drop-20 {
  100% {
    top: 110%;
    left: 25%;
  }
}
.confetti-wrapper .confetti-21 {
  width: 10px;
  height: 4px;
  background-color: #8bc34a;
  top: -10%;
  left: 70%;
  opacity: 0.6689804664;
  -webkit-transform: rotate(292.9229445321deg);
      -ms-transform: rotate(292.9229445321deg);
       -o-transform: rotate(292.9229445321deg);
          transform: rotate(292.9229445321deg);
  -webkit-animation: drop-21 4.9342856501s 0.9346120135s infinite;
       -o-animation: drop-21 4.9342856501s 0.9346120135s infinite;
          animation: drop-21 4.9342856501s 0.9346120135s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-21 {
  100% {
    top: 110%;
    left: 83%;
  }
}
@-o-keyframes drop-21 {
  100% {
    top: 110%;
    left: 83%;
  }
}
@keyframes drop-21 {
  100% {
    top: 110%;
    left: 83%;
  }
}
.confetti-wrapper .confetti-22 {
  width: 1px;
  height: 0.4px;
  background-color: #ffc107;
  top: -10%;
  left: 100%;
  opacity: 1.4684876596;
  -webkit-transform: rotate(306.8506082794deg);
      -ms-transform: rotate(306.8506082794deg);
       -o-transform: rotate(306.8506082794deg);
          transform: rotate(306.8506082794deg);
  -webkit-animation: drop-22 4.3239740726s 0.229171836s infinite;
       -o-animation: drop-22 4.3239740726s 0.229171836s infinite;
          animation: drop-22 4.3239740726s 0.229171836s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-22 {
  100% {
    top: 110%;
    left: 113%;
  }
}
@-o-keyframes drop-22 {
  100% {
    top: 110%;
    left: 113%;
  }
}
@keyframes drop-22 {
  100% {
    top: 110%;
    left: 113%;
  }
}
.confetti-wrapper .confetti-23 {
  width: 6px;
  height: 2.4px;
  background-color: #F44336;
  top: -10%;
  left: -1%;
  opacity: 1.078147777;
  -webkit-transform: rotate(28.7325332626deg);
      -ms-transform: rotate(28.7325332626deg);
       -o-transform: rotate(28.7325332626deg);
          transform: rotate(28.7325332626deg);
  -webkit-animation: drop-23 4.1730707493s 0.3759838754s infinite;
       -o-animation: drop-23 4.1730707493s 0.3759838754s infinite;
          animation: drop-23 4.1730707493s 0.3759838754s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-23 {
  100% {
    top: 110%;
    left: 10%;
  }
}
@-o-keyframes drop-23 {
  100% {
    top: 110%;
    left: 10%;
  }
}
@keyframes drop-23 {
  100% {
    top: 110%;
    left: 10%;
  }
}
.confetti-wrapper .confetti-24 {
  width: 3px;
  height: 1.2px;
  background-color: #673ab7;
  top: -10%;
  left: 97%;
  opacity: 0.5823172;
  -webkit-transform: rotate(30.460848855deg);
      -ms-transform: rotate(30.460848855deg);
       -o-transform: rotate(30.460848855deg);
          transform: rotate(30.460848855deg);
  -webkit-animation: drop-24 4.7405402349s 0.4583207208s infinite;
       -o-animation: drop-24 4.7405402349s 0.4583207208s infinite;
          animation: drop-24 4.7405402349s 0.4583207208s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-24 {
  100% {
    top: 110%;
    left: 104%;
  }
}
@-o-keyframes drop-24 {
  100% {
    top: 110%;
    left: 104%;
  }
}
@keyframes drop-24 {
  100% {
    top: 110%;
    left: 104%;
  }
}
.confetti-wrapper .confetti-25 {
  width: 2px;
  height: 0.8px;
  background-color: #F44336;
  top: -10%;
  left: 1%;
  opacity: 0.9918564538;
  -webkit-transform: rotate(12.0492335864deg);
      -ms-transform: rotate(12.0492335864deg);
       -o-transform: rotate(12.0492335864deg);
          transform: rotate(12.0492335864deg);
  -webkit-animation: drop-25 4.3010463977s 0.6086280189s infinite;
       -o-animation: drop-25 4.3010463977s 0.6086280189s infinite;
          animation: drop-25 4.3010463977s 0.6086280189s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-25 {
  100% {
    top: 110%;
    left: 10%;
  }
}
@-o-keyframes drop-25 {
  100% {
    top: 110%;
    left: 10%;
  }
}
@keyframes drop-25 {
  100% {
    top: 110%;
    left: 10%;
  }
}
.confetti-wrapper .confetti-26 {
  width: 4px;
  height: 1.6px;
  background-color: #00bcd4;
  top: -10%;
  left: 20%;
  opacity: 0.7940311842;
  -webkit-transform: rotate(171.463637965deg);
      -ms-transform: rotate(171.463637965deg);
       -o-transform: rotate(171.463637965deg);
          transform: rotate(171.463637965deg);
  -webkit-animation: drop-26 4.1011321746s 0.8053513384s infinite;
       -o-animation: drop-26 4.1011321746s 0.8053513384s infinite;
          animation: drop-26 4.1011321746s 0.8053513384s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-26 {
  100% {
    top: 110%;
    left: 26%;
  }
}
@-o-keyframes drop-26 {
  100% {
    top: 110%;
    left: 26%;
  }
}
@keyframes drop-26 {
  100% {
    top: 110%;
    left: 26%;
  }
}
.confetti-wrapper .confetti-27 {
  width: 4px;
  height: 1.6px;
  background-color: #3f51b5;
  top: -10%;
  left: 42%;
  opacity: 0.9397764686;
  -webkit-transform: rotate(287.5149526392deg);
      -ms-transform: rotate(287.5149526392deg);
       -o-transform: rotate(287.5149526392deg);
          transform: rotate(287.5149526392deg);
  -webkit-animation: drop-27 4.8788428735s 0.2391302668s infinite;
       -o-animation: drop-27 4.8788428735s 0.2391302668s infinite;
          animation: drop-27 4.8788428735s 0.2391302668s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-27 {
  100% {
    top: 110%;
    left: 51%;
  }
}
@-o-keyframes drop-27 {
  100% {
    top: 110%;
    left: 51%;
  }
}
@keyframes drop-27 {
  100% {
    top: 110%;
    left: 51%;
  }
}
.confetti-wrapper .confetti-28 {
  width: 5px;
  height: 2px;
  background-color: #C1BB00;
  top: -10%;
  left: 83%;
  opacity: 1.224265691;
  -webkit-transform: rotate(62.1106983957deg);
      -ms-transform: rotate(62.1106983957deg);
       -o-transform: rotate(62.1106983957deg);
          transform: rotate(62.1106983957deg);
  -webkit-animation: drop-28 4.7192048046s 0.5313438308s infinite;
       -o-animation: drop-28 4.7192048046s 0.5313438308s infinite;
          animation: drop-28 4.7192048046s 0.5313438308s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-28 {
  100% {
    top: 110%;
    left: 91%;
  }
}
@-o-keyframes drop-28 {
  100% {
    top: 110%;
    left: 91%;
  }
}
@keyframes drop-28 {
  100% {
    top: 110%;
    left: 91%;
  }
}
.confetti-wrapper .confetti-29 {
  width: 8px;
  height: 3.2px;
  background-color: #F44336;
  top: -10%;
  left: 49%;
  opacity: 1.4681605949;
  -webkit-transform: rotate(253.122779612deg);
      -ms-transform: rotate(253.122779612deg);
       -o-transform: rotate(253.122779612deg);
          transform: rotate(253.122779612deg);
  -webkit-animation: drop-29 4.7486236075s 0.0184367936s infinite;
       -o-animation: drop-29 4.7486236075s 0.0184367936s infinite;
          animation: drop-29 4.7486236075s 0.0184367936s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-29 {
  100% {
    top: 110%;
    left: 56%;
  }
}
@-o-keyframes drop-29 {
  100% {
    top: 110%;
    left: 56%;
  }
}
@keyframes drop-29 {
  100% {
    top: 110%;
    left: 56%;
  }
}
.confetti-wrapper .confetti-30 {
  width: 5px;
  height: 2px;
  background-color: #F44336;
  top: -10%;
  left: 55%;
  opacity: 1.290640256;
  -webkit-transform: rotate(33.6458794241deg);
      -ms-transform: rotate(33.6458794241deg);
       -o-transform: rotate(33.6458794241deg);
          transform: rotate(33.6458794241deg);
  -webkit-animation: drop-30 4.881718902s 0.0482385131s infinite;
       -o-animation: drop-30 4.881718902s 0.0482385131s infinite;
          animation: drop-30 4.881718902s 0.0482385131s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-30 {
  100% {
    top: 110%;
    left: 58%;
  }
}
@-o-keyframes drop-30 {
  100% {
    top: 110%;
    left: 58%;
  }
}
@keyframes drop-30 {
  100% {
    top: 110%;
    left: 58%;
  }
}
.confetti-wrapper .confetti-31 {
  width: 4px;
  height: 1.6px;
  background-color: #009688;
  top: -10%;
  left: 86%;
  opacity: 0.7918811194;
  -webkit-transform: rotate(183.4737957781deg);
      -ms-transform: rotate(183.4737957781deg);
       -o-transform: rotate(183.4737957781deg);
          transform: rotate(183.4737957781deg);
  -webkit-animation: drop-31 4.4593585419s 0.7945288702s infinite;
       -o-animation: drop-31 4.4593585419s 0.7945288702s infinite;
          animation: drop-31 4.4593585419s 0.7945288702s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-31 {
  100% {
    top: 110%;
    left: 88%;
  }
}
@-o-keyframes drop-31 {
  100% {
    top: 110%;
    left: 88%;
  }
}
@keyframes drop-31 {
  100% {
    top: 110%;
    left: 88%;
  }
}
.confetti-wrapper .confetti-32 {
  width: 1px;
  height: 0.4px;
  background-color: #ffc107;
  top: -10%;
  left: 29%;
  opacity: 1.3551524997;
  -webkit-transform: rotate(43.3738567557deg);
      -ms-transform: rotate(43.3738567557deg);
       -o-transform: rotate(43.3738567557deg);
          transform: rotate(43.3738567557deg);
  -webkit-animation: drop-32 4.8254447092s 0.5022225739s infinite;
       -o-animation: drop-32 4.8254447092s 0.5022225739s infinite;
          animation: drop-32 4.8254447092s 0.5022225739s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-32 {
  100% {
    top: 110%;
    left: 41%;
  }
}
@-o-keyframes drop-32 {
  100% {
    top: 110%;
    left: 41%;
  }
}
@keyframes drop-32 {
  100% {
    top: 110%;
    left: 41%;
  }
}
.confetti-wrapper .confetti-33 {
  width: 7px;
  height: 2.8px;
  background-color: #673ab7;
  top: -10%;
  left: 33%;
  opacity: 0.5471583997;
  -webkit-transform: rotate(86.6902838123deg);
      -ms-transform: rotate(86.6902838123deg);
       -o-transform: rotate(86.6902838123deg);
          transform: rotate(86.6902838123deg);
  -webkit-animation: drop-33 4.0339111356s 0.8033673729s infinite;
       -o-animation: drop-33 4.0339111356s 0.8033673729s infinite;
          animation: drop-33 4.0339111356s 0.8033673729s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-33 {
  100% {
    top: 110%;
    left: 35%;
  }
}
@-o-keyframes drop-33 {
  100% {
    top: 110%;
    left: 35%;
  }
}
@keyframes drop-33 {
  100% {
    top: 110%;
    left: 35%;
  }
}
.confetti-wrapper .confetti-34 {
  width: 10px;
  height: 4px;
  background-color: #03a9f4;
  top: -10%;
  left: -3%;
  opacity: 0.7417979435;
  -webkit-transform: rotate(178.1450736216deg);
      -ms-transform: rotate(178.1450736216deg);
       -o-transform: rotate(178.1450736216deg);
          transform: rotate(178.1450736216deg);
  -webkit-animation: drop-34 4.3134695382s 0.191133871s infinite;
       -o-animation: drop-34 4.3134695382s 0.191133871s infinite;
          animation: drop-34 4.3134695382s 0.191133871s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-34 {
  100% {
    top: 110%;
    left: 7%;
  }
}
@-o-keyframes drop-34 {
  100% {
    top: 110%;
    left: 7%;
  }
}
@keyframes drop-34 {
  100% {
    top: 110%;
    left: 7%;
  }
}
.confetti-wrapper .confetti-35 {
  width: 2px;
  height: 0.8px;
  background-color: #00bcd4;
  top: -10%;
  left: 90%;
  opacity: 1.2538838523;
  -webkit-transform: rotate(39.9581203666deg);
      -ms-transform: rotate(39.9581203666deg);
       -o-transform: rotate(39.9581203666deg);
          transform: rotate(39.9581203666deg);
  -webkit-animation: drop-35 4.869197012s 0.9292346458s infinite;
       -o-animation: drop-35 4.869197012s 0.9292346458s infinite;
          animation: drop-35 4.869197012s 0.9292346458s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-35 {
  100% {
    top: 110%;
    left: 98%;
  }
}
@-o-keyframes drop-35 {
  100% {
    top: 110%;
    left: 98%;
  }
}
@keyframes drop-35 {
  100% {
    top: 110%;
    left: 98%;
  }
}
.confetti-wrapper .confetti-36 {
  width: 2px;
  height: 0.8px;
  background-color: #4CAF50;
  top: -10%;
  left: -5%;
  opacity: 1.1047504439;
  -webkit-transform: rotate(280.2082639774deg);
      -ms-transform: rotate(280.2082639774deg);
       -o-transform: rotate(280.2082639774deg);
          transform: rotate(280.2082639774deg);
  -webkit-animation: drop-36 4.4375090811s 0.2461233774s infinite;
       -o-animation: drop-36 4.4375090811s 0.2461233774s infinite;
          animation: drop-36 4.4375090811s 0.2461233774s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-36 {
  100% {
    top: 110%;
    left: 9%;
  }
}
@-o-keyframes drop-36 {
  100% {
    top: 110%;
    left: 9%;
  }
}
@keyframes drop-36 {
  100% {
    top: 110%;
    left: 9%;
  }
}
.confetti-wrapper .confetti-37 {
  width: 8px;
  height: 3.2px;
  background-color: #673ab7;
  top: -10%;
  left: -1%;
  opacity: 1.0361047893;
  -webkit-transform: rotate(124.4967669778deg);
      -ms-transform: rotate(124.4967669778deg);
       -o-transform: rotate(124.4967669778deg);
          transform: rotate(124.4967669778deg);
  -webkit-animation: drop-37 4.5882474516s 0.384489855s infinite;
       -o-animation: drop-37 4.5882474516s 0.384489855s infinite;
          animation: drop-37 4.5882474516s 0.384489855s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-37 {
  100% {
    top: 110%;
    left: 2%;
  }
}
@-o-keyframes drop-37 {
  100% {
    top: 110%;
    left: 2%;
  }
}
@keyframes drop-37 {
  100% {
    top: 110%;
    left: 2%;
  }
}
.confetti-wrapper .confetti-38 {
  width: 2px;
  height: 0.8px;
  background-color: #673ab7;
  top: -10%;
  left: 85%;
  opacity: 1.203185017;
  -webkit-transform: rotate(132.1132485601deg);
      -ms-transform: rotate(132.1132485601deg);
       -o-transform: rotate(132.1132485601deg);
          transform: rotate(132.1132485601deg);
  -webkit-animation: drop-38 4.0545182098s 0.7740640148s infinite;
       -o-animation: drop-38 4.0545182098s 0.7740640148s infinite;
          animation: drop-38 4.0545182098s 0.7740640148s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-38 {
  100% {
    top: 110%;
    left: 90%;
  }
}
@-o-keyframes drop-38 {
  100% {
    top: 110%;
    left: 90%;
  }
}
@keyframes drop-38 {
  100% {
    top: 110%;
    left: 90%;
  }
}
.confetti-wrapper .confetti-39 {
  width: 6px;
  height: 2.4px;
  background-color: #009688;
  top: -10%;
  left: 58%;
  opacity: 0.5151891624;
  -webkit-transform: rotate(256.7499630604deg);
      -ms-transform: rotate(256.7499630604deg);
       -o-transform: rotate(256.7499630604deg);
          transform: rotate(256.7499630604deg);
  -webkit-animation: drop-39 4.6212828357s 0.3717375354s infinite;
       -o-animation: drop-39 4.6212828357s 0.3717375354s infinite;
          animation: drop-39 4.6212828357s 0.3717375354s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-39 {
  100% {
    top: 110%;
    left: 68%;
  }
}
@-o-keyframes drop-39 {
  100% {
    top: 110%;
    left: 68%;
  }
}
@keyframes drop-39 {
  100% {
    top: 110%;
    left: 68%;
  }
}
.confetti-wrapper .confetti-40 {
  width: 4px;
  height: 1.6px;
  background-color: #3f51b5;
  top: -10%;
  left: -7%;
  opacity: 0.5393779596;
  -webkit-transform: rotate(50.6819653241deg);
      -ms-transform: rotate(50.6819653241deg);
       -o-transform: rotate(50.6819653241deg);
          transform: rotate(50.6819653241deg);
  -webkit-animation: drop-40 4.474077623s 0.590084647s infinite;
       -o-animation: drop-40 4.474077623s 0.590084647s infinite;
          animation: drop-40 4.474077623s 0.590084647s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-40 {
  100% {
    top: 110%;
    left: 7%;
  }
}
@-o-keyframes drop-40 {
  100% {
    top: 110%;
    left: 7%;
  }
}
@keyframes drop-40 {
  100% {
    top: 110%;
    left: 7%;
  }
}
.confetti-wrapper .confetti-41 {
  width: 3px;
  height: 1.2px;
  background-color: #ff9800;
  top: -10%;
  left: 16%;
  opacity: 1.045054738;
  -webkit-transform: rotate(71.9518386455deg);
      -ms-transform: rotate(71.9518386455deg);
       -o-transform: rotate(71.9518386455deg);
          transform: rotate(71.9518386455deg);
  -webkit-animation: drop-41 4.4670486809s 0.3279037874s infinite;
       -o-animation: drop-41 4.4670486809s 0.3279037874s infinite;
          animation: drop-41 4.4670486809s 0.3279037874s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-41 {
  100% {
    top: 110%;
    left: 20%;
  }
}
@-o-keyframes drop-41 {
  100% {
    top: 110%;
    left: 20%;
  }
}
@keyframes drop-41 {
  100% {
    top: 110%;
    left: 20%;
  }
}
.confetti-wrapper .confetti-42 {
  width: 7px;
  height: 2.8px;
  background-color: #cddc39;
  top: -10%;
  left: 46%;
  opacity: 1.2117910784;
  -webkit-transform: rotate(49.1113394375deg);
      -ms-transform: rotate(49.1113394375deg);
       -o-transform: rotate(49.1113394375deg);
          transform: rotate(49.1113394375deg);
  -webkit-animation: drop-42 4.522580274s 0.8109844219s infinite;
       -o-animation: drop-42 4.522580274s 0.8109844219s infinite;
          animation: drop-42 4.522580274s 0.8109844219s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-42 {
  100% {
    top: 110%;
    left: 47%;
  }
}
@-o-keyframes drop-42 {
  100% {
    top: 110%;
    left: 47%;
  }
}
@keyframes drop-42 {
  100% {
    top: 110%;
    left: 47%;
  }
}
.confetti-wrapper .confetti-43 {
  width: 8px;
  height: 3.2px;
  background-color: #03a9f4;
  top: -10%;
  left: 45%;
  opacity: 0.739668711;
  -webkit-transform: rotate(152.7419782555deg);
      -ms-transform: rotate(152.7419782555deg);
       -o-transform: rotate(152.7419782555deg);
          transform: rotate(152.7419782555deg);
  -webkit-animation: drop-43 4.6781249029s 0.8353266813s infinite;
       -o-animation: drop-43 4.6781249029s 0.8353266813s infinite;
          animation: drop-43 4.6781249029s 0.8353266813s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-43 {
  100% {
    top: 110%;
    left: 54%;
  }
}
@-o-keyframes drop-43 {
  100% {
    top: 110%;
    left: 54%;
  }
}
@keyframes drop-43 {
  100% {
    top: 110%;
    left: 54%;
  }
}
.confetti-wrapper .confetti-44 {
  width: 1px;
  height: 0.4px;
  background-color: #ff9800;
  top: -10%;
  left: 27%;
  opacity: 0.8884401544;
  -webkit-transform: rotate(229.0823719021deg);
      -ms-transform: rotate(229.0823719021deg);
       -o-transform: rotate(229.0823719021deg);
          transform: rotate(229.0823719021deg);
  -webkit-animation: drop-44 4.1944464372s 0.2374519929s infinite;
       -o-animation: drop-44 4.1944464372s 0.2374519929s infinite;
          animation: drop-44 4.1944464372s 0.2374519929s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-44 {
  100% {
    top: 110%;
    left: 39%;
  }
}
@-o-keyframes drop-44 {
  100% {
    top: 110%;
    left: 39%;
  }
}
@keyframes drop-44 {
  100% {
    top: 110%;
    left: 39%;
  }
}
.confetti-wrapper .confetti-45 {
  width: 8px;
  height: 3.2px;
  background-color: #3f51b5;
  top: -10%;
  left: 48%;
  opacity: 1.2568181684;
  -webkit-transform: rotate(261.6442604523deg);
      -ms-transform: rotate(261.6442604523deg);
       -o-transform: rotate(261.6442604523deg);
          transform: rotate(261.6442604523deg);
  -webkit-animation: drop-45 4.7373977636s 0.8305535104s infinite;
       -o-animation: drop-45 4.7373977636s 0.8305535104s infinite;
          animation: drop-45 4.7373977636s 0.8305535104s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-45 {
  100% {
    top: 110%;
    left: 49%;
  }
}
@-o-keyframes drop-45 {
  100% {
    top: 110%;
    left: 49%;
  }
}
@keyframes drop-45 {
  100% {
    top: 110%;
    left: 49%;
  }
}
.confetti-wrapper .confetti-46 {
  width: 4px;
  height: 1.6px;
  background-color: #cddc39;
  top: -10%;
  left: 20%;
  opacity: 0.9117237579;
  -webkit-transform: rotate(162.2999389456deg);
      -ms-transform: rotate(162.2999389456deg);
       -o-transform: rotate(162.2999389456deg);
          transform: rotate(162.2999389456deg);
  -webkit-animation: drop-46 4.1125677856s 0.1444363944s infinite;
       -o-animation: drop-46 4.1125677856s 0.1444363944s infinite;
          animation: drop-46 4.1125677856s 0.1444363944s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-46 {
  100% {
    top: 110%;
    left: 22%;
  }
}
@-o-keyframes drop-46 {
  100% {
    top: 110%;
    left: 22%;
  }
}
@keyframes drop-46 {
  100% {
    top: 110%;
    left: 22%;
  }
}
.confetti-wrapper .confetti-47 {
  width: 8px;
  height: 3.2px;
  background-color: #ff9800;
  top: -10%;
  left: 93%;
  opacity: 1.3473228285;
  -webkit-transform: rotate(280.6531320503deg);
      -ms-transform: rotate(280.6531320503deg);
       -o-transform: rotate(280.6531320503deg);
          transform: rotate(280.6531320503deg);
  -webkit-animation: drop-47 4.7102535566s 0.5241063779s infinite;
       -o-animation: drop-47 4.7102535566s 0.5241063779s infinite;
          animation: drop-47 4.7102535566s 0.5241063779s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-47 {
  100% {
    top: 110%;
    left: 100%;
  }
}
@-o-keyframes drop-47 {
  100% {
    top: 110%;
    left: 100%;
  }
}
@keyframes drop-47 {
  100% {
    top: 110%;
    left: 100%;
  }
}
.confetti-wrapper .confetti-48 {
  width: 1px;
  height: 0.4px;
  background-color: #3f51b5;
  top: -10%;
  left: 41%;
  opacity: 0.6203971149;
  -webkit-transform: rotate(191.7744190131deg);
      -ms-transform: rotate(191.7744190131deg);
       -o-transform: rotate(191.7744190131deg);
          transform: rotate(191.7744190131deg);
  -webkit-animation: drop-48 4.7317141508s 0.9237042514s infinite;
       -o-animation: drop-48 4.7317141508s 0.9237042514s infinite;
          animation: drop-48 4.7317141508s 0.9237042514s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-48 {
  100% {
    top: 110%;
    left: 46%;
  }
}
@-o-keyframes drop-48 {
  100% {
    top: 110%;
    left: 46%;
  }
}
@keyframes drop-48 {
  100% {
    top: 110%;
    left: 46%;
  }
}
.confetti-wrapper .confetti-49 {
  width: 4px;
  height: 1.6px;
  background-color: #F44336;
  top: -10%;
  left: 54%;
  opacity: 0.8899122283;
  -webkit-transform: rotate(250.480679092deg);
      -ms-transform: rotate(250.480679092deg);
       -o-transform: rotate(250.480679092deg);
          transform: rotate(250.480679092deg);
  -webkit-animation: drop-49 4.5016565703s 0.4926802275s infinite;
       -o-animation: drop-49 4.5016565703s 0.4926802275s infinite;
          animation: drop-49 4.5016565703s 0.4926802275s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-49 {
  100% {
    top: 110%;
    left: 68%;
  }
}
@-o-keyframes drop-49 {
  100% {
    top: 110%;
    left: 68%;
  }
}
@keyframes drop-49 {
  100% {
    top: 110%;
    left: 68%;
  }
}
.confetti-wrapper .confetti-50 {
  width: 9px;
  height: 3.6px;
  background-color: #ff9800;
  top: -10%;
  left: 24%;
  opacity: 1.248622218;
  -webkit-transform: rotate(240.5181020983deg);
      -ms-transform: rotate(240.5181020983deg);
       -o-transform: rotate(240.5181020983deg);
          transform: rotate(240.5181020983deg);
  -webkit-animation: drop-50 4.5951101244s 0.4058051504s infinite;
       -o-animation: drop-50 4.5951101244s 0.4058051504s infinite;
          animation: drop-50 4.5951101244s 0.4058051504s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-50 {
  100% {
    top: 110%;
    left: 27%;
  }
}
@-o-keyframes drop-50 {
  100% {
    top: 110%;
    left: 27%;
  }
}
@keyframes drop-50 {
  100% {
    top: 110%;
    left: 27%;
  }
}
.confetti-wrapper .confetti-51 {
  width: 7px;
  height: 2.8px;
  background-color: #8bc34a;
  top: -10%;
  left: 31%;
  opacity: 0.9543653976;
  -webkit-transform: rotate(235.8425309149deg);
      -ms-transform: rotate(235.8425309149deg);
       -o-transform: rotate(235.8425309149deg);
          transform: rotate(235.8425309149deg);
  -webkit-animation: drop-51 4.9653451614s 0.7105276461s infinite;
       -o-animation: drop-51 4.9653451614s 0.7105276461s infinite;
          animation: drop-51 4.9653451614s 0.7105276461s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-51 {
  100% {
    top: 110%;
    left: 39%;
  }
}
@-o-keyframes drop-51 {
  100% {
    top: 110%;
    left: 39%;
  }
}
@keyframes drop-51 {
  100% {
    top: 110%;
    left: 39%;
  }
}
.confetti-wrapper .confetti-52 {
  width: 1px;
  height: 0.4px;
  background-color: #009688;
  top: -10%;
  left: 53%;
  opacity: 1.0711878497;
  -webkit-transform: rotate(224.6262697173deg);
      -ms-transform: rotate(224.6262697173deg);
       -o-transform: rotate(224.6262697173deg);
          transform: rotate(224.6262697173deg);
  -webkit-animation: drop-52 4.5304727006s 0.9957163215s infinite;
       -o-animation: drop-52 4.5304727006s 0.9957163215s infinite;
          animation: drop-52 4.5304727006s 0.9957163215s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-52 {
  100% {
    top: 110%;
    left: 60%;
  }
}
@-o-keyframes drop-52 {
  100% {
    top: 110%;
    left: 60%;
  }
}
@keyframes drop-52 {
  100% {
    top: 110%;
    left: 60%;
  }
}
.confetti-wrapper .confetti-53 {
  width: 10px;
  height: 4px;
  background-color: #F44336;
  top: -10%;
  left: -4%;
  opacity: 1.2461088064;
  -webkit-transform: rotate(77.1722401015deg);
      -ms-transform: rotate(77.1722401015deg);
       -o-transform: rotate(77.1722401015deg);
          transform: rotate(77.1722401015deg);
  -webkit-animation: drop-53 4.9467404465s 0.6485544903s infinite;
       -o-animation: drop-53 4.9467404465s 0.6485544903s infinite;
          animation: drop-53 4.9467404465s 0.6485544903s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-53 {
  100% {
    top: 110%;
    left: 1%;
  }
}
@-o-keyframes drop-53 {
  100% {
    top: 110%;
    left: 1%;
  }
}
@keyframes drop-53 {
  100% {
    top: 110%;
    left: 1%;
  }
}
.confetti-wrapper .confetti-54 {
  width: 2px;
  height: 0.8px;
  background-color: #00bcd4;
  top: -10%;
  left: 78%;
  opacity: 1.1919795266;
  -webkit-transform: rotate(147.520169756deg);
      -ms-transform: rotate(147.520169756deg);
       -o-transform: rotate(147.520169756deg);
          transform: rotate(147.520169756deg);
  -webkit-animation: drop-54 4.3612660792s 0.5773665492s infinite;
       -o-animation: drop-54 4.3612660792s 0.5773665492s infinite;
          animation: drop-54 4.3612660792s 0.5773665492s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-54 {
  100% {
    top: 110%;
    left: 91%;
  }
}
@-o-keyframes drop-54 {
  100% {
    top: 110%;
    left: 91%;
  }
}
@keyframes drop-54 {
  100% {
    top: 110%;
    left: 91%;
  }
}
.confetti-wrapper .confetti-55 {
  width: 6px;
  height: 2.4px;
  background-color: #3f51b5;
  top: -10%;
  left: 93%;
  opacity: 0.5207349538;
  -webkit-transform: rotate(332.5397857784deg);
      -ms-transform: rotate(332.5397857784deg);
       -o-transform: rotate(332.5397857784deg);
          transform: rotate(332.5397857784deg);
  -webkit-animation: drop-55 4.1280336128s 0.1309010895s infinite;
       -o-animation: drop-55 4.1280336128s 0.1309010895s infinite;
          animation: drop-55 4.1280336128s 0.1309010895s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-55 {
  100% {
    top: 110%;
    left: 98%;
  }
}
@-o-keyframes drop-55 {
  100% {
    top: 110%;
    left: 98%;
  }
}
@keyframes drop-55 {
  100% {
    top: 110%;
    left: 98%;
  }
}
.confetti-wrapper .confetti-56 {
  width: 5px;
  height: 2px;
  background-color: #00bcd4;
  top: -10%;
  left: 82%;
  opacity: 0.6899834658;
  -webkit-transform: rotate(74.3315844127deg);
      -ms-transform: rotate(74.3315844127deg);
       -o-transform: rotate(74.3315844127deg);
          transform: rotate(74.3315844127deg);
  -webkit-animation: drop-56 4.1637315962s 0.1975071653s infinite;
       -o-animation: drop-56 4.1637315962s 0.1975071653s infinite;
          animation: drop-56 4.1637315962s 0.1975071653s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-56 {
  100% {
    top: 110%;
    left: 89%;
  }
}
@-o-keyframes drop-56 {
  100% {
    top: 110%;
    left: 89%;
  }
}
@keyframes drop-56 {
  100% {
    top: 110%;
    left: 89%;
  }
}
.confetti-wrapper .confetti-57 {
  width: 1px;
  height: 0.4px;
  background-color: #00bcd4;
  top: -10%;
  left: 12%;
  opacity: 0.6518436644;
  -webkit-transform: rotate(87.3482822843deg);
      -ms-transform: rotate(87.3482822843deg);
       -o-transform: rotate(87.3482822843deg);
          transform: rotate(87.3482822843deg);
  -webkit-animation: drop-57 4.9110293323s 0.0427995761s infinite;
       -o-animation: drop-57 4.9110293323s 0.0427995761s infinite;
          animation: drop-57 4.9110293323s 0.0427995761s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-57 {
  100% {
    top: 110%;
    left: 26%;
  }
}
@-o-keyframes drop-57 {
  100% {
    top: 110%;
    left: 26%;
  }
}
@keyframes drop-57 {
  100% {
    top: 110%;
    left: 26%;
  }
}
.confetti-wrapper .confetti-58 {
  width: 2px;
  height: 0.8px;
  background-color: #e91e63;
  top: -10%;
  left: 29%;
  opacity: 0.7759568728;
  -webkit-transform: rotate(107.1320672078deg);
      -ms-transform: rotate(107.1320672078deg);
       -o-transform: rotate(107.1320672078deg);
          transform: rotate(107.1320672078deg);
  -webkit-animation: drop-58 4.2619709119s 0.5696231867s infinite;
       -o-animation: drop-58 4.2619709119s 0.5696231867s infinite;
          animation: drop-58 4.2619709119s 0.5696231867s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-58 {
  100% {
    top: 110%;
    left: 31%;
  }
}
@-o-keyframes drop-58 {
  100% {
    top: 110%;
    left: 31%;
  }
}
@keyframes drop-58 {
  100% {
    top: 110%;
    left: 31%;
  }
}
.confetti-wrapper .confetti-59 {
  width: 10px;
  height: 4px;
  background-color: #ff9800;
  top: -10%;
  left: 74%;
  opacity: 0.8028349675;
  -webkit-transform: rotate(159.2799925158deg);
      -ms-transform: rotate(159.2799925158deg);
       -o-transform: rotate(159.2799925158deg);
          transform: rotate(159.2799925158deg);
  -webkit-animation: drop-59 4.3702254752s 0.0137090888s infinite;
       -o-animation: drop-59 4.3702254752s 0.0137090888s infinite;
          animation: drop-59 4.3702254752s 0.0137090888s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-59 {
  100% {
    top: 110%;
    left: 84%;
  }
}
@-o-keyframes drop-59 {
  100% {
    top: 110%;
    left: 84%;
  }
}
@keyframes drop-59 {
  100% {
    top: 110%;
    left: 84%;
  }
}
.confetti-wrapper .confetti-60 {
  width: 9px;
  height: 3.6px;
  background-color: #9c27b0;
  top: -10%;
  left: 40%;
  opacity: 1.4071017139;
  -webkit-transform: rotate(217.17444361deg);
      -ms-transform: rotate(217.17444361deg);
       -o-transform: rotate(217.17444361deg);
          transform: rotate(217.17444361deg);
  -webkit-animation: drop-60 4.4689765025s 0.2499028284s infinite;
       -o-animation: drop-60 4.4689765025s 0.2499028284s infinite;
          animation: drop-60 4.4689765025s 0.2499028284s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-60 {
  100% {
    top: 110%;
    left: 49%;
  }
}
@-o-keyframes drop-60 {
  100% {
    top: 110%;
    left: 49%;
  }
}
@keyframes drop-60 {
  100% {
    top: 110%;
    left: 49%;
  }
}
.confetti-wrapper .confetti-61 {
  width: 7px;
  height: 2.8px;
  background-color: #4CAF50;
  top: -10%;
  left: -1%;
  opacity: 1.3082880237;
  -webkit-transform: rotate(329.0229606682deg);
      -ms-transform: rotate(329.0229606682deg);
       -o-transform: rotate(329.0229606682deg);
          transform: rotate(329.0229606682deg);
  -webkit-animation: drop-61 4.3313259425s 0.4609188777s infinite;
       -o-animation: drop-61 4.3313259425s 0.4609188777s infinite;
          animation: drop-61 4.3313259425s 0.4609188777s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-61 {
  100% {
    top: 110%;
    left: 0%;
  }
}
@-o-keyframes drop-61 {
  100% {
    top: 110%;
    left: 0%;
  }
}
@keyframes drop-61 {
  100% {
    top: 110%;
    left: 0%;
  }
}
.confetti-wrapper .confetti-62 {
  width: 2px;
  height: 0.8px;
  background-color: #ffc107;
  top: -10%;
  left: 41%;
  opacity: 0.9726187285;
  -webkit-transform: rotate(300.1711803038deg);
      -ms-transform: rotate(300.1711803038deg);
       -o-transform: rotate(300.1711803038deg);
          transform: rotate(300.1711803038deg);
  -webkit-animation: drop-62 4.8460257599s 0.8739601161s infinite;
       -o-animation: drop-62 4.8460257599s 0.8739601161s infinite;
          animation: drop-62 4.8460257599s 0.8739601161s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-62 {
  100% {
    top: 110%;
    left: 55%;
  }
}
@-o-keyframes drop-62 {
  100% {
    top: 110%;
    left: 55%;
  }
}
@keyframes drop-62 {
  100% {
    top: 110%;
    left: 55%;
  }
}
.confetti-wrapper .confetti-63 {
  width: 8px;
  height: 3.2px;
  background-color: #009688;
  top: -10%;
  left: -3%;
  opacity: 0.5859847757;
  -webkit-transform: rotate(85.4773375785deg);
      -ms-transform: rotate(85.4773375785deg);
       -o-transform: rotate(85.4773375785deg);
          transform: rotate(85.4773375785deg);
  -webkit-animation: drop-63 4.019635342s 0.8226237201s infinite;
       -o-animation: drop-63 4.019635342s 0.8226237201s infinite;
          animation: drop-63 4.019635342s 0.8226237201s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-63 {
  100% {
    top: 110%;
    left: 5%;
  }
}
@-o-keyframes drop-63 {
  100% {
    top: 110%;
    left: 5%;
  }
}
@keyframes drop-63 {
  100% {
    top: 110%;
    left: 5%;
  }
}
.confetti-wrapper .confetti-64 {
  width: 4px;
  height: 1.6px;
  background-color: #ff5722;
  top: -10%;
  left: -4%;
  opacity: 0.9497321773;
  -webkit-transform: rotate(32.8909894299deg);
      -ms-transform: rotate(32.8909894299deg);
       -o-transform: rotate(32.8909894299deg);
          transform: rotate(32.8909894299deg);
  -webkit-animation: drop-64 4.4011316313s 0.5892774943s infinite;
       -o-animation: drop-64 4.4011316313s 0.5892774943s infinite;
          animation: drop-64 4.4011316313s 0.5892774943s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-64 {
  100% {
    top: 110%;
    left: 0%;
  }
}
@-o-keyframes drop-64 {
  100% {
    top: 110%;
    left: 0%;
  }
}
@keyframes drop-64 {
  100% {
    top: 110%;
    left: 0%;
  }
}
.confetti-wrapper .confetti-65 {
  width: 8px;
  height: 3.2px;
  background-color: #3f51b5;
  top: -10%;
  left: 75%;
  opacity: 0.5269878058;
  -webkit-transform: rotate(180.7619529153deg);
      -ms-transform: rotate(180.7619529153deg);
       -o-transform: rotate(180.7619529153deg);
          transform: rotate(180.7619529153deg);
  -webkit-animation: drop-65 4.3012804739s 0.5858983213s infinite;
       -o-animation: drop-65 4.3012804739s 0.5858983213s infinite;
          animation: drop-65 4.3012804739s 0.5858983213s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-65 {
  100% {
    top: 110%;
    left: 84%;
  }
}
@-o-keyframes drop-65 {
  100% {
    top: 110%;
    left: 84%;
  }
}
@keyframes drop-65 {
  100% {
    top: 110%;
    left: 84%;
  }
}
.confetti-wrapper .confetti-66 {
  width: 7px;
  height: 2.8px;
  background-color: #4CAF50;
  top: -10%;
  left: 9%;
  opacity: 0.9331730229;
  -webkit-transform: rotate(131.9872022359deg);
      -ms-transform: rotate(131.9872022359deg);
       -o-transform: rotate(131.9872022359deg);
          transform: rotate(131.9872022359deg);
  -webkit-animation: drop-66 4.9537381861s 0.1945864671s infinite;
       -o-animation: drop-66 4.9537381861s 0.1945864671s infinite;
          animation: drop-66 4.9537381861s 0.1945864671s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-66 {
  100% {
    top: 110%;
    left: 10%;
  }
}
@-o-keyframes drop-66 {
  100% {
    top: 110%;
    left: 10%;
  }
}
@keyframes drop-66 {
  100% {
    top: 110%;
    left: 10%;
  }
}
.confetti-wrapper .confetti-67 {
  width: 3px;
  height: 1.2px;
  background-color: #4CAF50;
  top: -10%;
  left: 52%;
  opacity: 0.6098101927;
  -webkit-transform: rotate(118.3107704669deg);
      -ms-transform: rotate(118.3107704669deg);
       -o-transform: rotate(118.3107704669deg);
          transform: rotate(118.3107704669deg);
  -webkit-animation: drop-67 4.8593127224s 0.6215121824s infinite;
       -o-animation: drop-67 4.8593127224s 0.6215121824s infinite;
          animation: drop-67 4.8593127224s 0.6215121824s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-67 {
  100% {
    top: 110%;
    left: 57%;
  }
}
@-o-keyframes drop-67 {
  100% {
    top: 110%;
    left: 57%;
  }
}
@keyframes drop-67 {
  100% {
    top: 110%;
    left: 57%;
  }
}
.confetti-wrapper .confetti-68 {
  width: 8px;
  height: 3.2px;
  background-color: #cddc39;
  top: -10%;
  left: 51%;
  opacity: 1.1985392251;
  -webkit-transform: rotate(14.7480764003deg);
      -ms-transform: rotate(14.7480764003deg);
       -o-transform: rotate(14.7480764003deg);
          transform: rotate(14.7480764003deg);
  -webkit-animation: drop-68 4.8608602755s 0.1751308417s infinite;
       -o-animation: drop-68 4.8608602755s 0.1751308417s infinite;
          animation: drop-68 4.8608602755s 0.1751308417s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-68 {
  100% {
    top: 110%;
    left: 65%;
  }
}
@-o-keyframes drop-68 {
  100% {
    top: 110%;
    left: 65%;
  }
}
@keyframes drop-68 {
  100% {
    top: 110%;
    left: 65%;
  }
}
.confetti-wrapper .confetti-69 {
  width: 3px;
  height: 1.2px;
  background-color: #00bcd4;
  top: -10%;
  left: 21%;
  opacity: 1.4274544067;
  -webkit-transform: rotate(266.202516977deg);
      -ms-transform: rotate(266.202516977deg);
       -o-transform: rotate(266.202516977deg);
          transform: rotate(266.202516977deg);
  -webkit-animation: drop-69 4.4580582554s 0.768177396s infinite;
       -o-animation: drop-69 4.4580582554s 0.768177396s infinite;
          animation: drop-69 4.4580582554s 0.768177396s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-69 {
  100% {
    top: 110%;
    left: 35%;
  }
}
@-o-keyframes drop-69 {
  100% {
    top: 110%;
    left: 35%;
  }
}
@keyframes drop-69 {
  100% {
    top: 110%;
    left: 35%;
  }
}
.confetti-wrapper .confetti-70 {
  width: 5px;
  height: 2px;
  background-color: #8bc34a;
  top: -10%;
  left: 53%;
  opacity: 1.2526157253;
  -webkit-transform: rotate(348.9643029719deg);
      -ms-transform: rotate(348.9643029719deg);
       -o-transform: rotate(348.9643029719deg);
          transform: rotate(348.9643029719deg);
  -webkit-animation: drop-70 4.430086057s 0.2512744472s infinite;
       -o-animation: drop-70 4.430086057s 0.2512744472s infinite;
          animation: drop-70 4.430086057s 0.2512744472s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-70 {
  100% {
    top: 110%;
    left: 67%;
  }
}
@-o-keyframes drop-70 {
  100% {
    top: 110%;
    left: 67%;
  }
}
@keyframes drop-70 {
  100% {
    top: 110%;
    left: 67%;
  }
}
.confetti-wrapper .confetti-71 {
  width: 6px;
  height: 2.4px;
  background-color: #3f51b5;
  top: -10%;
  left: 0%;
  opacity: 0.7966246004;
  -webkit-transform: rotate(55.2276379274deg);
      -ms-transform: rotate(55.2276379274deg);
       -o-transform: rotate(55.2276379274deg);
          transform: rotate(55.2276379274deg);
  -webkit-animation: drop-71 4.1336176762s 0.6974283121s infinite;
       -o-animation: drop-71 4.1336176762s 0.6974283121s infinite;
          animation: drop-71 4.1336176762s 0.6974283121s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-71 {
  100% {
    top: 110%;
    left: 6%;
  }
}
@-o-keyframes drop-71 {
  100% {
    top: 110%;
    left: 6%;
  }
}
@keyframes drop-71 {
  100% {
    top: 110%;
    left: 6%;
  }
}
.confetti-wrapper .confetti-72 {
  width: 3px;
  height: 1.2px;
  background-color: #ffc107;
  top: -10%;
  left: 32%;
  opacity: 1.1527266914;
  -webkit-transform: rotate(226.5758865828deg);
      -ms-transform: rotate(226.5758865828deg);
       -o-transform: rotate(226.5758865828deg);
          transform: rotate(226.5758865828deg);
  -webkit-animation: drop-72 4.5717811083s 0.3073673834s infinite;
       -o-animation: drop-72 4.5717811083s 0.3073673834s infinite;
          animation: drop-72 4.5717811083s 0.3073673834s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-72 {
  100% {
    top: 110%;
    left: 42%;
  }
}
@-o-keyframes drop-72 {
  100% {
    top: 110%;
    left: 42%;
  }
}
@keyframes drop-72 {
  100% {
    top: 110%;
    left: 42%;
  }
}
.confetti-wrapper .confetti-73 {
  width: 1px;
  height: 0.4px;
  background-color: #cddc39;
  top: -10%;
  left: -1%;
  opacity: 1.2162609188;
  -webkit-transform: rotate(214.8997711235deg);
      -ms-transform: rotate(214.8997711235deg);
       -o-transform: rotate(214.8997711235deg);
          transform: rotate(214.8997711235deg);
  -webkit-animation: drop-73 4.5496698298s 0.3079810778s infinite;
       -o-animation: drop-73 4.5496698298s 0.3079810778s infinite;
          animation: drop-73 4.5496698298s 0.3079810778s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-73 {
  100% {
    top: 110%;
    left: 2%;
  }
}
@-o-keyframes drop-73 {
  100% {
    top: 110%;
    left: 2%;
  }
}
@keyframes drop-73 {
  100% {
    top: 110%;
    left: 2%;
  }
}
.confetti-wrapper .confetti-74 {
  width: 4px;
  height: 1.6px;
  background-color: #009688;
  top: -10%;
  left: -7%;
  opacity: 0.8031185514;
  -webkit-transform: rotate(49.1486226701deg);
      -ms-transform: rotate(49.1486226701deg);
       -o-transform: rotate(49.1486226701deg);
          transform: rotate(49.1486226701deg);
  -webkit-animation: drop-74 4.2566926698s 0.0775723982s infinite;
       -o-animation: drop-74 4.2566926698s 0.0775723982s infinite;
          animation: drop-74 4.2566926698s 0.0775723982s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-74 {
  100% {
    top: 110%;
    left: -6%;
  }
}
@-o-keyframes drop-74 {
  100% {
    top: 110%;
    left: -6%;
  }
}
@keyframes drop-74 {
  100% {
    top: 110%;
    left: -6%;
  }
}
.confetti-wrapper .confetti-75 {
  width: 7px;
  height: 2.8px;
  background-color: #C1BB00;
  top: -10%;
  left: 81%;
  opacity: 0.6085138306;
  -webkit-transform: rotate(63.8907318929deg);
      -ms-transform: rotate(63.8907318929deg);
       -o-transform: rotate(63.8907318929deg);
          transform: rotate(63.8907318929deg);
  -webkit-animation: drop-75 4.7007837919s 0.7738786154s infinite;
       -o-animation: drop-75 4.7007837919s 0.7738786154s infinite;
          animation: drop-75 4.7007837919s 0.7738786154s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-75 {
  100% {
    top: 110%;
    left: 83%;
  }
}
@-o-keyframes drop-75 {
  100% {
    top: 110%;
    left: 83%;
  }
}
@keyframes drop-75 {
  100% {
    top: 110%;
    left: 83%;
  }
}
.confetti-wrapper .confetti-76 {
  width: 10px;
  height: 4px;
  background-color: #3f51b5;
  top: -10%;
  left: -6%;
  opacity: 0.7313736008;
  -webkit-transform: rotate(214.7771753065deg);
      -ms-transform: rotate(214.7771753065deg);
       -o-transform: rotate(214.7771753065deg);
          transform: rotate(214.7771753065deg);
  -webkit-animation: drop-76 4.0362445911s 0.2517686866s infinite;
       -o-animation: drop-76 4.0362445911s 0.2517686866s infinite;
          animation: drop-76 4.0362445911s 0.2517686866s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-76 {
  100% {
    top: 110%;
    left: 0%;
  }
}
@-o-keyframes drop-76 {
  100% {
    top: 110%;
    left: 0%;
  }
}
@keyframes drop-76 {
  100% {
    top: 110%;
    left: 0%;
  }
}
.confetti-wrapper .confetti-77 {
  width: 4px;
  height: 1.6px;
  background-color: #009688;
  top: -10%;
  left: 68%;
  opacity: 0.6791750678;
  -webkit-transform: rotate(353.0830617408deg);
      -ms-transform: rotate(353.0830617408deg);
       -o-transform: rotate(353.0830617408deg);
          transform: rotate(353.0830617408deg);
  -webkit-animation: drop-77 4.291939093s 0.1539525496s infinite;
       -o-animation: drop-77 4.291939093s 0.1539525496s infinite;
          animation: drop-77 4.291939093s 0.1539525496s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-77 {
  100% {
    top: 110%;
    left: 70%;
  }
}
@-o-keyframes drop-77 {
  100% {
    top: 110%;
    left: 70%;
  }
}
@keyframes drop-77 {
  100% {
    top: 110%;
    left: 70%;
  }
}
.confetti-wrapper .confetti-78 {
  width: 8px;
  height: 3.2px;
  background-color: #8bc34a;
  top: -10%;
  left: 72%;
  opacity: 0.740327231;
  -webkit-transform: rotate(4.1957733923deg);
      -ms-transform: rotate(4.1957733923deg);
       -o-transform: rotate(4.1957733923deg);
          transform: rotate(4.1957733923deg);
  -webkit-animation: drop-78 4.8027485548s 0.5129306671s infinite;
       -o-animation: drop-78 4.8027485548s 0.5129306671s infinite;
          animation: drop-78 4.8027485548s 0.5129306671s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-78 {
  100% {
    top: 110%;
    left: 79%;
  }
}
@-o-keyframes drop-78 {
  100% {
    top: 110%;
    left: 79%;
  }
}
@keyframes drop-78 {
  100% {
    top: 110%;
    left: 79%;
  }
}
.confetti-wrapper .confetti-79 {
  width: 1px;
  height: 0.4px;
  background-color: #3f51b5;
  top: -10%;
  left: 9%;
  opacity: 1.0249918673;
  -webkit-transform: rotate(317.9012120465deg);
      -ms-transform: rotate(317.9012120465deg);
       -o-transform: rotate(317.9012120465deg);
          transform: rotate(317.9012120465deg);
  -webkit-animation: drop-79 4.3663162262s 0.9754461378s infinite;
       -o-animation: drop-79 4.3663162262s 0.9754461378s infinite;
          animation: drop-79 4.3663162262s 0.9754461378s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-79 {
  100% {
    top: 110%;
    left: 21%;
  }
}
@-o-keyframes drop-79 {
  100% {
    top: 110%;
    left: 21%;
  }
}
@keyframes drop-79 {
  100% {
    top: 110%;
    left: 21%;
  }
}
.confetti-wrapper .confetti-80 {
  width: 8px;
  height: 3.2px;
  background-color: #009688;
  top: -10%;
  left: 68%;
  opacity: 1.0149332108;
  -webkit-transform: rotate(12.6802371674deg);
      -ms-transform: rotate(12.6802371674deg);
       -o-transform: rotate(12.6802371674deg);
          transform: rotate(12.6802371674deg);
  -webkit-animation: drop-80 4.1427055217s 0.8957690936s infinite;
       -o-animation: drop-80 4.1427055217s 0.8957690936s infinite;
          animation: drop-80 4.1427055217s 0.8957690936s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-80 {
  100% {
    top: 110%;
    left: 82%;
  }
}
@-o-keyframes drop-80 {
  100% {
    top: 110%;
    left: 82%;
  }
}
@keyframes drop-80 {
  100% {
    top: 110%;
    left: 82%;
  }
}
.confetti-wrapper .confetti-81 {
  width: 7px;
  height: 2.8px;
  background-color: #8bc34a;
  top: -10%;
  left: 43%;
  opacity: 1.4172638544;
  -webkit-transform: rotate(277.150621582deg);
      -ms-transform: rotate(277.150621582deg);
       -o-transform: rotate(277.150621582deg);
          transform: rotate(277.150621582deg);
  -webkit-animation: drop-81 4.3703761627s 0.746176856s infinite;
       -o-animation: drop-81 4.3703761627s 0.746176856s infinite;
          animation: drop-81 4.3703761627s 0.746176856s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-81 {
  100% {
    top: 110%;
    left: 58%;
  }
}
@-o-keyframes drop-81 {
  100% {
    top: 110%;
    left: 58%;
  }
}
@keyframes drop-81 {
  100% {
    top: 110%;
    left: 58%;
  }
}
.confetti-wrapper .confetti-82 {
  width: 9px;
  height: 3.6px;
  background-color: #009688;
  top: -10%;
  left: 26%;
  opacity: 1.3846504934;
  -webkit-transform: rotate(108.1678177716deg);
      -ms-transform: rotate(108.1678177716deg);
       -o-transform: rotate(108.1678177716deg);
          transform: rotate(108.1678177716deg);
  -webkit-animation: drop-82 4.1960432067s 0.0111352783s infinite;
       -o-animation: drop-82 4.1960432067s 0.0111352783s infinite;
          animation: drop-82 4.1960432067s 0.0111352783s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-82 {
  100% {
    top: 110%;
    left: 39%;
  }
}
@-o-keyframes drop-82 {
  100% {
    top: 110%;
    left: 39%;
  }
}
@keyframes drop-82 {
  100% {
    top: 110%;
    left: 39%;
  }
}
.confetti-wrapper .confetti-83 {
  width: 8px;
  height: 3.2px;
  background-color: #F44336;
  top: -10%;
  left: 98%;
  opacity: 0.566623281;
  -webkit-transform: rotate(330.9806852652deg);
      -ms-transform: rotate(330.9806852652deg);
       -o-transform: rotate(330.9806852652deg);
          transform: rotate(330.9806852652deg);
  -webkit-animation: drop-83 4.9250557527s 0.1078992021s infinite;
       -o-animation: drop-83 4.9250557527s 0.1078992021s infinite;
          animation: drop-83 4.9250557527s 0.1078992021s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-83 {
  100% {
    top: 110%;
    left: 112%;
  }
}
@-o-keyframes drop-83 {
  100% {
    top: 110%;
    left: 112%;
  }
}
@keyframes drop-83 {
  100% {
    top: 110%;
    left: 112%;
  }
}
.confetti-wrapper .confetti-84 {
  width: 3px;
  height: 1.2px;
  background-color: #e91e63;
  top: -10%;
  left: 95%;
  opacity: 1.0955829788;
  -webkit-transform: rotate(42.5482281135deg);
      -ms-transform: rotate(42.5482281135deg);
       -o-transform: rotate(42.5482281135deg);
          transform: rotate(42.5482281135deg);
  -webkit-animation: drop-84 4.426386585s 0.666975242s infinite;
       -o-animation: drop-84 4.426386585s 0.666975242s infinite;
          animation: drop-84 4.426386585s 0.666975242s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-84 {
  100% {
    top: 110%;
    left: 109%;
  }
}
@-o-keyframes drop-84 {
  100% {
    top: 110%;
    left: 109%;
  }
}
@keyframes drop-84 {
  100% {
    top: 110%;
    left: 109%;
  }
}
.confetti-wrapper .confetti-85 {
  width: 3px;
  height: 1.2px;
  background-color: #00bcd4;
  top: -10%;
  left: 39%;
  opacity: 1.3330453729;
  -webkit-transform: rotate(329.0592520221deg);
      -ms-transform: rotate(329.0592520221deg);
       -o-transform: rotate(329.0592520221deg);
          transform: rotate(329.0592520221deg);
  -webkit-animation: drop-85 4.0546661845s 0.2674841436s infinite;
       -o-animation: drop-85 4.0546661845s 0.2674841436s infinite;
          animation: drop-85 4.0546661845s 0.2674841436s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-85 {
  100% {
    top: 110%;
    left: 45%;
  }
}
@-o-keyframes drop-85 {
  100% {
    top: 110%;
    left: 45%;
  }
}
@keyframes drop-85 {
  100% {
    top: 110%;
    left: 45%;
  }
}
.confetti-wrapper .confetti-86 {
  width: 4px;
  height: 1.6px;
  background-color: #3f51b5;
  top: -10%;
  left: 93%;
  opacity: 1.4269485741;
  -webkit-transform: rotate(200.8234960915deg);
      -ms-transform: rotate(200.8234960915deg);
       -o-transform: rotate(200.8234960915deg);
          transform: rotate(200.8234960915deg);
  -webkit-animation: drop-86 4.4921170615s 0.9780708595s infinite;
       -o-animation: drop-86 4.4921170615s 0.9780708595s infinite;
          animation: drop-86 4.4921170615s 0.9780708595s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-86 {
  100% {
    top: 110%;
    left: 98%;
  }
}
@-o-keyframes drop-86 {
  100% {
    top: 110%;
    left: 98%;
  }
}
@keyframes drop-86 {
  100% {
    top: 110%;
    left: 98%;
  }
}
.confetti-wrapper .confetti-87 {
  width: 7px;
  height: 2.8px;
  background-color: #00bcd4;
  top: -10%;
  left: 38%;
  opacity: 0.7321456646;
  -webkit-transform: rotate(278.9651680573deg);
      -ms-transform: rotate(278.9651680573deg);
       -o-transform: rotate(278.9651680573deg);
          transform: rotate(278.9651680573deg);
  -webkit-animation: drop-87 4.3801577819s 0.2151746229s infinite;
       -o-animation: drop-87 4.3801577819s 0.2151746229s infinite;
          animation: drop-87 4.3801577819s 0.2151746229s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-87 {
  100% {
    top: 110%;
    left: 39%;
  }
}
@-o-keyframes drop-87 {
  100% {
    top: 110%;
    left: 39%;
  }
}
@keyframes drop-87 {
  100% {
    top: 110%;
    left: 39%;
  }
}
.confetti-wrapper .confetti-88 {
  width: 7px;
  height: 2.8px;
  background-color: #3f51b5;
  top: -10%;
  left: 34%;
  opacity: 0.5270366436;
  -webkit-transform: rotate(67.0271340846deg);
      -ms-transform: rotate(67.0271340846deg);
       -o-transform: rotate(67.0271340846deg);
          transform: rotate(67.0271340846deg);
  -webkit-animation: drop-88 4.4447135146s 0.0051339116s infinite;
       -o-animation: drop-88 4.4447135146s 0.0051339116s infinite;
          animation: drop-88 4.4447135146s 0.0051339116s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-88 {
  100% {
    top: 110%;
    left: 36%;
  }
}
@-o-keyframes drop-88 {
  100% {
    top: 110%;
    left: 36%;
  }
}
@keyframes drop-88 {
  100% {
    top: 110%;
    left: 36%;
  }
}
.confetti-wrapper .confetti-89 {
  width: 7px;
  height: 2.8px;
  background-color: #e91e63;
  top: -10%;
  left: 97%;
  opacity: 0.5786544903;
  -webkit-transform: rotate(211.9393846182deg);
      -ms-transform: rotate(211.9393846182deg);
       -o-transform: rotate(211.9393846182deg);
          transform: rotate(211.9393846182deg);
  -webkit-animation: drop-89 4.0439768479s 0.046097587s infinite;
       -o-animation: drop-89 4.0439768479s 0.046097587s infinite;
          animation: drop-89 4.0439768479s 0.046097587s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-89 {
  100% {
    top: 110%;
    left: 102%;
  }
}
@-o-keyframes drop-89 {
  100% {
    top: 110%;
    left: 102%;
  }
}
@keyframes drop-89 {
  100% {
    top: 110%;
    left: 102%;
  }
}
.confetti-wrapper .confetti-90 {
  width: 3px;
  height: 1.2px;
  background-color: #cddc39;
  top: -10%;
  left: 100%;
  opacity: 1.0556075622;
  -webkit-transform: rotate(280.8873632105deg);
      -ms-transform: rotate(280.8873632105deg);
       -o-transform: rotate(280.8873632105deg);
          transform: rotate(280.8873632105deg);
  -webkit-animation: drop-90 4.0964867255s 0.2443065501s infinite;
       -o-animation: drop-90 4.0964867255s 0.2443065501s infinite;
          animation: drop-90 4.0964867255s 0.2443065501s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-90 {
  100% {
    top: 110%;
    left: 115%;
  }
}
@-o-keyframes drop-90 {
  100% {
    top: 110%;
    left: 115%;
  }
}
@keyframes drop-90 {
  100% {
    top: 110%;
    left: 115%;
  }
}
.confetti-wrapper .confetti-91 {
  width: 3px;
  height: 1.2px;
  background-color: #ff9800;
  top: -10%;
  left: 96%;
  opacity: 0.7511050829;
  -webkit-transform: rotate(210.9845884697deg);
      -ms-transform: rotate(210.9845884697deg);
       -o-transform: rotate(210.9845884697deg);
          transform: rotate(210.9845884697deg);
  -webkit-animation: drop-91 4.421437471s 0.1497003863s infinite;
       -o-animation: drop-91 4.421437471s 0.1497003863s infinite;
          animation: drop-91 4.421437471s 0.1497003863s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-91 {
  100% {
    top: 110%;
    left: 110%;
  }
}
@-o-keyframes drop-91 {
  100% {
    top: 110%;
    left: 110%;
  }
}
@keyframes drop-91 {
  100% {
    top: 110%;
    left: 110%;
  }
}
.confetti-wrapper .confetti-92 {
  width: 3px;
  height: 1.2px;
  background-color: #ffc107;
  top: -10%;
  left: 63%;
  opacity: 0.9542597945;
  -webkit-transform: rotate(238.2289146276deg);
      -ms-transform: rotate(238.2289146276deg);
       -o-transform: rotate(238.2289146276deg);
          transform: rotate(238.2289146276deg);
  -webkit-animation: drop-92 4.9523318032s 0.6747109844s infinite;
       -o-animation: drop-92 4.9523318032s 0.6747109844s infinite;
          animation: drop-92 4.9523318032s 0.6747109844s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-92 {
  100% {
    top: 110%;
    left: 70%;
  }
}
@-o-keyframes drop-92 {
  100% {
    top: 110%;
    left: 70%;
  }
}
@keyframes drop-92 {
  100% {
    top: 110%;
    left: 70%;
  }
}
.confetti-wrapper .confetti-93 {
  width: 4px;
  height: 1.6px;
  background-color: #cddc39;
  top: -10%;
  left: -7%;
  opacity: 1.4291534795;
  -webkit-transform: rotate(192.3985793608deg);
      -ms-transform: rotate(192.3985793608deg);
       -o-transform: rotate(192.3985793608deg);
          transform: rotate(192.3985793608deg);
  -webkit-animation: drop-93 4.4017245787s 0.6890451156s infinite;
       -o-animation: drop-93 4.4017245787s 0.6890451156s infinite;
          animation: drop-93 4.4017245787s 0.6890451156s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-93 {
  100% {
    top: 110%;
    left: -5%;
  }
}
@-o-keyframes drop-93 {
  100% {
    top: 110%;
    left: -5%;
  }
}
@keyframes drop-93 {
  100% {
    top: 110%;
    left: -5%;
  }
}
.confetti-wrapper .confetti-94 {
  width: 2px;
  height: 0.8px;
  background-color: #cddc39;
  top: -10%;
  left: 38%;
  opacity: 0.9211180215;
  -webkit-transform: rotate(18.9152742011deg);
      -ms-transform: rotate(18.9152742011deg);
       -o-transform: rotate(18.9152742011deg);
          transform: rotate(18.9152742011deg);
  -webkit-animation: drop-94 4.0630161122s 0.9466386954s infinite;
       -o-animation: drop-94 4.0630161122s 0.9466386954s infinite;
          animation: drop-94 4.0630161122s 0.9466386954s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-94 {
  100% {
    top: 110%;
    left: 53%;
  }
}
@-o-keyframes drop-94 {
  100% {
    top: 110%;
    left: 53%;
  }
}
@keyframes drop-94 {
  100% {
    top: 110%;
    left: 53%;
  }
}
.confetti-wrapper .confetti-95 {
  width: 3px;
  height: 1.2px;
  background-color: #9c27b0;
  top: -10%;
  left: 3%;
  opacity: 0.6307661961;
  -webkit-transform: rotate(193.487972148deg);
      -ms-transform: rotate(193.487972148deg);
       -o-transform: rotate(193.487972148deg);
          transform: rotate(193.487972148deg);
  -webkit-animation: drop-95 4.0311499457s 0.2162516979s infinite;
       -o-animation: drop-95 4.0311499457s 0.2162516979s infinite;
          animation: drop-95 4.0311499457s 0.2162516979s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-95 {
  100% {
    top: 110%;
    left: 10%;
  }
}
@-o-keyframes drop-95 {
  100% {
    top: 110%;
    left: 10%;
  }
}
@keyframes drop-95 {
  100% {
    top: 110%;
    left: 10%;
  }
}
.confetti-wrapper .confetti-96 {
  width: 2px;
  height: 0.8px;
  background-color: #673ab7;
  top: -10%;
  left: 11%;
  opacity: 1.3146526892;
  -webkit-transform: rotate(351.608723122deg);
      -ms-transform: rotate(351.608723122deg);
       -o-transform: rotate(351.608723122deg);
          transform: rotate(351.608723122deg);
  -webkit-animation: drop-96 4.9684232312s 0.2029136708s infinite;
       -o-animation: drop-96 4.9684232312s 0.2029136708s infinite;
          animation: drop-96 4.9684232312s 0.2029136708s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-96 {
  100% {
    top: 110%;
    left: 19%;
  }
}
@-o-keyframes drop-96 {
  100% {
    top: 110%;
    left: 19%;
  }
}
@keyframes drop-96 {
  100% {
    top: 110%;
    left: 19%;
  }
}
.confetti-wrapper .confetti-97 {
  width: 8px;
  height: 3.2px;
  background-color: #F44336;
  top: -10%;
  left: 38%;
  opacity: 0.6879607467;
  -webkit-transform: rotate(145.3581057167deg);
      -ms-transform: rotate(145.3581057167deg);
       -o-transform: rotate(145.3581057167deg);
          transform: rotate(145.3581057167deg);
  -webkit-animation: drop-97 4.9154663967s 0.3801497129s infinite;
       -o-animation: drop-97 4.9154663967s 0.3801497129s infinite;
          animation: drop-97 4.9154663967s 0.3801497129s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-97 {
  100% {
    top: 110%;
    left: 41%;
  }
}
@-o-keyframes drop-97 {
  100% {
    top: 110%;
    left: 41%;
  }
}
@keyframes drop-97 {
  100% {
    top: 110%;
    left: 41%;
  }
}
.confetti-wrapper .confetti-98 {
  width: 2px;
  height: 0.8px;
  background-color: #ff9800;
  top: -10%;
  left: 16%;
  opacity: 1.268184058;
  -webkit-transform: rotate(250.9632077794deg);
      -ms-transform: rotate(250.9632077794deg);
       -o-transform: rotate(250.9632077794deg);
          transform: rotate(250.9632077794deg);
  -webkit-animation: drop-98 4.2968231724s 0.9879155184s infinite;
       -o-animation: drop-98 4.2968231724s 0.9879155184s infinite;
          animation: drop-98 4.2968231724s 0.9879155184s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-98 {
  100% {
    top: 110%;
    left: 21%;
  }
}
@-o-keyframes drop-98 {
  100% {
    top: 110%;
    left: 21%;
  }
}
@keyframes drop-98 {
  100% {
    top: 110%;
    left: 21%;
  }
}
.confetti-wrapper .confetti-99 {
  width: 10px;
  height: 4px;
  background-color: #ff9800;
  top: -10%;
  left: 0%;
  opacity: 0.5572608759;
  -webkit-transform: rotate(348.5519503689deg);
      -ms-transform: rotate(348.5519503689deg);
       -o-transform: rotate(348.5519503689deg);
          transform: rotate(348.5519503689deg);
  -webkit-animation: drop-99 4.1438705761s 0.8732152043s infinite;
       -o-animation: drop-99 4.1438705761s 0.8732152043s infinite;
          animation: drop-99 4.1438705761s 0.8732152043s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-99 {
  100% {
    top: 110%;
    left: 11%;
  }
}
@-o-keyframes drop-99 {
  100% {
    top: 110%;
    left: 11%;
  }
}
@keyframes drop-99 {
  100% {
    top: 110%;
    left: 11%;
  }
}
.confetti-wrapper .confetti-100 {
  width: 9px;
  height: 3.6px;
  background-color: #673ab7;
  top: -10%;
  left: 99%;
  opacity: 1.4654323144;
  -webkit-transform: rotate(334.6923142338deg);
      -ms-transform: rotate(334.6923142338deg);
       -o-transform: rotate(334.6923142338deg);
          transform: rotate(334.6923142338deg);
  -webkit-animation: drop-100 4.893829464s 0.1875704801s infinite;
       -o-animation: drop-100 4.893829464s 0.1875704801s infinite;
          animation: drop-100 4.893829464s 0.1875704801s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-100 {
  100% {
    top: 110%;
    left: 101%;
  }
}
@-o-keyframes drop-100 {
  100% {
    top: 110%;
    left: 101%;
  }
}
@keyframes drop-100 {
  100% {
    top: 110%;
    left: 101%;
  }
}
.confetti-wrapper .confetti-101 {
  width: 2px;
  height: 0.8px;
  background-color: #4CAF50;
  top: -10%;
  left: 99%;
  opacity: 0.9161929625;
  -webkit-transform: rotate(163.9709287172deg);
      -ms-transform: rotate(163.9709287172deg);
       -o-transform: rotate(163.9709287172deg);
          transform: rotate(163.9709287172deg);
  -webkit-animation: drop-101 4.801426509s 0.6262233378s infinite;
       -o-animation: drop-101 4.801426509s 0.6262233378s infinite;
          animation: drop-101 4.801426509s 0.6262233378s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-101 {
  100% {
    top: 110%;
    left: 104%;
  }
}
@-o-keyframes drop-101 {
  100% {
    top: 110%;
    left: 104%;
  }
}
@keyframes drop-101 {
  100% {
    top: 110%;
    left: 104%;
  }
}
.confetti-wrapper .confetti-102 {
  width: 3px;
  height: 1.2px;
  background-color: #e91e63;
  top: -10%;
  left: 60%;
  opacity: 1.201787188;
  -webkit-transform: rotate(308.6517170463deg);
      -ms-transform: rotate(308.6517170463deg);
       -o-transform: rotate(308.6517170463deg);
          transform: rotate(308.6517170463deg);
  -webkit-animation: drop-102 4.5724002097s 0.8851636254s infinite;
       -o-animation: drop-102 4.5724002097s 0.8851636254s infinite;
          animation: drop-102 4.5724002097s 0.8851636254s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-102 {
  100% {
    top: 110%;
    left: 67%;
  }
}
@-o-keyframes drop-102 {
  100% {
    top: 110%;
    left: 67%;
  }
}
@keyframes drop-102 {
  100% {
    top: 110%;
    left: 67%;
  }
}
.confetti-wrapper .confetti-103 {
  width: 2px;
  height: 0.8px;
  background-color: #ffc107;
  top: -10%;
  left: 22%;
  opacity: 1.2460223455;
  -webkit-transform: rotate(24.4701742614deg);
      -ms-transform: rotate(24.4701742614deg);
       -o-transform: rotate(24.4701742614deg);
          transform: rotate(24.4701742614deg);
  -webkit-animation: drop-103 4.8391191332s 0.4817433045s infinite;
       -o-animation: drop-103 4.8391191332s 0.4817433045s infinite;
          animation: drop-103 4.8391191332s 0.4817433045s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-103 {
  100% {
    top: 110%;
    left: 28%;
  }
}
@-o-keyframes drop-103 {
  100% {
    top: 110%;
    left: 28%;
  }
}
@keyframes drop-103 {
  100% {
    top: 110%;
    left: 28%;
  }
}
.confetti-wrapper .confetti-104 {
  width: 5px;
  height: 2px;
  background-color: #e91e63;
  top: -10%;
  left: 24%;
  opacity: 1.4685355567;
  -webkit-transform: rotate(174.6849164098deg);
      -ms-transform: rotate(174.6849164098deg);
       -o-transform: rotate(174.6849164098deg);
          transform: rotate(174.6849164098deg);
  -webkit-animation: drop-104 4.669101611s 0.4140779662s infinite;
       -o-animation: drop-104 4.669101611s 0.4140779662s infinite;
          animation: drop-104 4.669101611s 0.4140779662s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-104 {
  100% {
    top: 110%;
    left: 28%;
  }
}
@-o-keyframes drop-104 {
  100% {
    top: 110%;
    left: 28%;
  }
}
@keyframes drop-104 {
  100% {
    top: 110%;
    left: 28%;
  }
}
.confetti-wrapper .confetti-105 {
  width: 5px;
  height: 2px;
  background-color: #4CAF50;
  top: -10%;
  left: 93%;
  opacity: 0.9928212532;
  -webkit-transform: rotate(21.8066555723deg);
      -ms-transform: rotate(21.8066555723deg);
       -o-transform: rotate(21.8066555723deg);
          transform: rotate(21.8066555723deg);
  -webkit-animation: drop-105 4.2452526912s 0.6834171058s infinite;
       -o-animation: drop-105 4.2452526912s 0.6834171058s infinite;
          animation: drop-105 4.2452526912s 0.6834171058s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-105 {
  100% {
    top: 110%;
    left: 102%;
  }
}
@-o-keyframes drop-105 {
  100% {
    top: 110%;
    left: 102%;
  }
}
@keyframes drop-105 {
  100% {
    top: 110%;
    left: 102%;
  }
}
.confetti-wrapper .confetti-106 {
  width: 9px;
  height: 3.6px;
  background-color: #3f51b5;
  top: -10%;
  left: 99%;
  opacity: 1.2484636819;
  -webkit-transform: rotate(255.0506694646deg);
      -ms-transform: rotate(255.0506694646deg);
       -o-transform: rotate(255.0506694646deg);
          transform: rotate(255.0506694646deg);
  -webkit-animation: drop-106 4.9119973051s 0.4982547926s infinite;
       -o-animation: drop-106 4.9119973051s 0.4982547926s infinite;
          animation: drop-106 4.9119973051s 0.4982547926s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-106 {
  100% {
    top: 110%;
    left: 110%;
  }
}
@-o-keyframes drop-106 {
  100% {
    top: 110%;
    left: 110%;
  }
}
@keyframes drop-106 {
  100% {
    top: 110%;
    left: 110%;
  }
}
.confetti-wrapper .confetti-107 {
  width: 7px;
  height: 2.8px;
  background-color: #03a9f4;
  top: -10%;
  left: 34%;
  opacity: 1.1056953564;
  -webkit-transform: rotate(283.222691101deg);
      -ms-transform: rotate(283.222691101deg);
       -o-transform: rotate(283.222691101deg);
          transform: rotate(283.222691101deg);
  -webkit-animation: drop-107 4.9172687199s 0.5883437047s infinite;
       -o-animation: drop-107 4.9172687199s 0.5883437047s infinite;
          animation: drop-107 4.9172687199s 0.5883437047s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-107 {
  100% {
    top: 110%;
    left: 40%;
  }
}
@-o-keyframes drop-107 {
  100% {
    top: 110%;
    left: 40%;
  }
}
@keyframes drop-107 {
  100% {
    top: 110%;
    left: 40%;
  }
}
.confetti-wrapper .confetti-108 {
  width: 5px;
  height: 2px;
  background-color: #e91e63;
  top: -10%;
  left: 3%;
  opacity: 1.2685237096;
  -webkit-transform: rotate(336.9373033475deg);
      -ms-transform: rotate(336.9373033475deg);
       -o-transform: rotate(336.9373033475deg);
          transform: rotate(336.9373033475deg);
  -webkit-animation: drop-108 4.6627892864s 0.0790264989s infinite;
       -o-animation: drop-108 4.6627892864s 0.0790264989s infinite;
          animation: drop-108 4.6627892864s 0.0790264989s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-108 {
  100% {
    top: 110%;
    left: 11%;
  }
}
@-o-keyframes drop-108 {
  100% {
    top: 110%;
    left: 11%;
  }
}
@keyframes drop-108 {
  100% {
    top: 110%;
    left: 11%;
  }
}
.confetti-wrapper .confetti-109 {
  width: 3px;
  height: 1.2px;
  background-color: #00bcd4;
  top: -10%;
  left: 79%;
  opacity: 1.1586080273;
  -webkit-transform: rotate(93.8127968645deg);
      -ms-transform: rotate(93.8127968645deg);
       -o-transform: rotate(93.8127968645deg);
          transform: rotate(93.8127968645deg);
  -webkit-animation: drop-109 4.3811690266s 0.8066472922s infinite;
       -o-animation: drop-109 4.3811690266s 0.8066472922s infinite;
          animation: drop-109 4.3811690266s 0.8066472922s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-109 {
  100% {
    top: 110%;
    left: 86%;
  }
}
@-o-keyframes drop-109 {
  100% {
    top: 110%;
    left: 86%;
  }
}
@keyframes drop-109 {
  100% {
    top: 110%;
    left: 86%;
  }
}
.confetti-wrapper .confetti-110 {
  width: 1px;
  height: 0.4px;
  background-color: #3f51b5;
  top: -10%;
  left: -7%;
  opacity: 0.772643097;
  -webkit-transform: rotate(200.707494923deg);
      -ms-transform: rotate(200.707494923deg);
       -o-transform: rotate(200.707494923deg);
          transform: rotate(200.707494923deg);
  -webkit-animation: drop-110 4.8769409857s 0.0306530488s infinite;
       -o-animation: drop-110 4.8769409857s 0.0306530488s infinite;
          animation: drop-110 4.8769409857s 0.0306530488s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-110 {
  100% {
    top: 110%;
    left: 2%;
  }
}
@-o-keyframes drop-110 {
  100% {
    top: 110%;
    left: 2%;
  }
}
@keyframes drop-110 {
  100% {
    top: 110%;
    left: 2%;
  }
}
.confetti-wrapper .confetti-111 {
  width: 6px;
  height: 2.4px;
  background-color: #C1BB00;
  top: -10%;
  left: 30%;
  opacity: 1.2803319342;
  -webkit-transform: rotate(269.6418185308deg);
      -ms-transform: rotate(269.6418185308deg);
       -o-transform: rotate(269.6418185308deg);
          transform: rotate(269.6418185308deg);
  -webkit-animation: drop-111 4.8517193359s 0.1475814881s infinite;
       -o-animation: drop-111 4.8517193359s 0.1475814881s infinite;
          animation: drop-111 4.8517193359s 0.1475814881s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-111 {
  100% {
    top: 110%;
    left: 38%;
  }
}
@-o-keyframes drop-111 {
  100% {
    top: 110%;
    left: 38%;
  }
}
@keyframes drop-111 {
  100% {
    top: 110%;
    left: 38%;
  }
}
.confetti-wrapper .confetti-112 {
  width: 10px;
  height: 4px;
  background-color: #C1BB00;
  top: -10%;
  left: 12%;
  opacity: 1.3715012008;
  -webkit-transform: rotate(352.2653214954deg);
      -ms-transform: rotate(352.2653214954deg);
       -o-transform: rotate(352.2653214954deg);
          transform: rotate(352.2653214954deg);
  -webkit-animation: drop-112 4.6549044264s 0.0060602856s infinite;
       -o-animation: drop-112 4.6549044264s 0.0060602856s infinite;
          animation: drop-112 4.6549044264s 0.0060602856s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-112 {
  100% {
    top: 110%;
    left: 25%;
  }
}
@-o-keyframes drop-112 {
  100% {
    top: 110%;
    left: 25%;
  }
}
@keyframes drop-112 {
  100% {
    top: 110%;
    left: 25%;
  }
}
.confetti-wrapper .confetti-113 {
  width: 6px;
  height: 2.4px;
  background-color: #009688;
  top: -10%;
  left: 47%;
  opacity: 0.7940908602;
  -webkit-transform: rotate(22.4734688678deg);
      -ms-transform: rotate(22.4734688678deg);
       -o-transform: rotate(22.4734688678deg);
          transform: rotate(22.4734688678deg);
  -webkit-animation: drop-113 4.6243216821s 0.3638704778s infinite;
       -o-animation: drop-113 4.6243216821s 0.3638704778s infinite;
          animation: drop-113 4.6243216821s 0.3638704778s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-113 {
  100% {
    top: 110%;
    left: 56%;
  }
}
@-o-keyframes drop-113 {
  100% {
    top: 110%;
    left: 56%;
  }
}
@keyframes drop-113 {
  100% {
    top: 110%;
    left: 56%;
  }
}
.confetti-wrapper .confetti-114 {
  width: 7px;
  height: 2.8px;
  background-color: #cddc39;
  top: -10%;
  left: 6%;
  opacity: 1.4757240439;
  -webkit-transform: rotate(57.607390011deg);
      -ms-transform: rotate(57.607390011deg);
       -o-transform: rotate(57.607390011deg);
          transform: rotate(57.607390011deg);
  -webkit-animation: drop-114 4.9885444847s 0.0105898191s infinite;
       -o-animation: drop-114 4.9885444847s 0.0105898191s infinite;
          animation: drop-114 4.9885444847s 0.0105898191s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-114 {
  100% {
    top: 110%;
    left: 7%;
  }
}
@-o-keyframes drop-114 {
  100% {
    top: 110%;
    left: 7%;
  }
}
@keyframes drop-114 {
  100% {
    top: 110%;
    left: 7%;
  }
}
.confetti-wrapper .confetti-115 {
  width: 4px;
  height: 1.6px;
  background-color: #e91e63;
  top: -10%;
  left: 54%;
  opacity: 0.6314636921;
  -webkit-transform: rotate(142.8612056947deg);
      -ms-transform: rotate(142.8612056947deg);
       -o-transform: rotate(142.8612056947deg);
          transform: rotate(142.8612056947deg);
  -webkit-animation: drop-115 4.9690031689s 0.0419414748s infinite;
       -o-animation: drop-115 4.9690031689s 0.0419414748s infinite;
          animation: drop-115 4.9690031689s 0.0419414748s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-115 {
  100% {
    top: 110%;
    left: 62%;
  }
}
@-o-keyframes drop-115 {
  100% {
    top: 110%;
    left: 62%;
  }
}
@keyframes drop-115 {
  100% {
    top: 110%;
    left: 62%;
  }
}
.confetti-wrapper .confetti-116 {
  width: 9px;
  height: 3.6px;
  background-color: #03a9f4;
  top: -10%;
  left: 53%;
  opacity: 0.7990627372;
  -webkit-transform: rotate(86.6357407912deg);
      -ms-transform: rotate(86.6357407912deg);
       -o-transform: rotate(86.6357407912deg);
          transform: rotate(86.6357407912deg);
  -webkit-animation: drop-116 4.9817717719s 0.5602474556s infinite;
       -o-animation: drop-116 4.9817717719s 0.5602474556s infinite;
          animation: drop-116 4.9817717719s 0.5602474556s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-116 {
  100% {
    top: 110%;
    left: 54%;
  }
}
@-o-keyframes drop-116 {
  100% {
    top: 110%;
    left: 54%;
  }
}
@keyframes drop-116 {
  100% {
    top: 110%;
    left: 54%;
  }
}
.confetti-wrapper .confetti-117 {
  width: 9px;
  height: 3.6px;
  background-color: #ff5722;
  top: -10%;
  left: 30%;
  opacity: 1.103754609;
  -webkit-transform: rotate(325.9906424416deg);
      -ms-transform: rotate(325.9906424416deg);
       -o-transform: rotate(325.9906424416deg);
          transform: rotate(325.9906424416deg);
  -webkit-animation: drop-117 4.4443203271s 0.5165072765s infinite;
       -o-animation: drop-117 4.4443203271s 0.5165072765s infinite;
          animation: drop-117 4.4443203271s 0.5165072765s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-117 {
  100% {
    top: 110%;
    left: 34%;
  }
}
@-o-keyframes drop-117 {
  100% {
    top: 110%;
    left: 34%;
  }
}
@keyframes drop-117 {
  100% {
    top: 110%;
    left: 34%;
  }
}
.confetti-wrapper .confetti-118 {
  width: 6px;
  height: 2.4px;
  background-color: #8bc34a;
  top: -10%;
  left: 74%;
  opacity: 0.637296315;
  -webkit-transform: rotate(202.3904874932deg);
      -ms-transform: rotate(202.3904874932deg);
       -o-transform: rotate(202.3904874932deg);
          transform: rotate(202.3904874932deg);
  -webkit-animation: drop-118 4.2253005008s 0.6998025204s infinite;
       -o-animation: drop-118 4.2253005008s 0.6998025204s infinite;
          animation: drop-118 4.2253005008s 0.6998025204s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-118 {
  100% {
    top: 110%;
    left: 88%;
  }
}
@-o-keyframes drop-118 {
  100% {
    top: 110%;
    left: 88%;
  }
}
@keyframes drop-118 {
  100% {
    top: 110%;
    left: 88%;
  }
}
.confetti-wrapper .confetti-119 {
  width: 1px;
  height: 0.4px;
  background-color: #F44336;
  top: -10%;
  left: 28%;
  opacity: 1.3471739163;
  -webkit-transform: rotate(259.6365194172deg);
      -ms-transform: rotate(259.6365194172deg);
       -o-transform: rotate(259.6365194172deg);
          transform: rotate(259.6365194172deg);
  -webkit-animation: drop-119 4.0192180149s 0.9946269108s infinite;
       -o-animation: drop-119 4.0192180149s 0.9946269108s infinite;
          animation: drop-119 4.0192180149s 0.9946269108s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-119 {
  100% {
    top: 110%;
    left: 31%;
  }
}
@-o-keyframes drop-119 {
  100% {
    top: 110%;
    left: 31%;
  }
}
@keyframes drop-119 {
  100% {
    top: 110%;
    left: 31%;
  }
}
.confetti-wrapper .confetti-120 {
  width: 1px;
  height: 0.4px;
  background-color: #9c27b0;
  top: -10%;
  left: 12%;
  opacity: 0.5824342607;
  -webkit-transform: rotate(104.3323294805deg);
      -ms-transform: rotate(104.3323294805deg);
       -o-transform: rotate(104.3323294805deg);
          transform: rotate(104.3323294805deg);
  -webkit-animation: drop-120 4.6022375547s 0.0336240346s infinite;
       -o-animation: drop-120 4.6022375547s 0.0336240346s infinite;
          animation: drop-120 4.6022375547s 0.0336240346s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-120 {
  100% {
    top: 110%;
    left: 22%;
  }
}
@-o-keyframes drop-120 {
  100% {
    top: 110%;
    left: 22%;
  }
}
@keyframes drop-120 {
  100% {
    top: 110%;
    left: 22%;
  }
}
.confetti-wrapper .confetti-121 {
  width: 9px;
  height: 3.6px;
  background-color: #3f51b5;
  top: -10%;
  left: 30%;
  opacity: 0.6996954713;
  -webkit-transform: rotate(344.9119310544deg);
      -ms-transform: rotate(344.9119310544deg);
       -o-transform: rotate(344.9119310544deg);
          transform: rotate(344.9119310544deg);
  -webkit-animation: drop-121 4.452456082s 0.4309791218s infinite;
       -o-animation: drop-121 4.452456082s 0.4309791218s infinite;
          animation: drop-121 4.452456082s 0.4309791218s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-121 {
  100% {
    top: 110%;
    left: 39%;
  }
}
@-o-keyframes drop-121 {
  100% {
    top: 110%;
    left: 39%;
  }
}
@keyframes drop-121 {
  100% {
    top: 110%;
    left: 39%;
  }
}
.confetti-wrapper .confetti-122 {
  width: 10px;
  height: 4px;
  background-color: #4CAF50;
  top: -10%;
  left: 50%;
  opacity: 1.0847662175;
  -webkit-transform: rotate(207.612357099deg);
      -ms-transform: rotate(207.612357099deg);
       -o-transform: rotate(207.612357099deg);
          transform: rotate(207.612357099deg);
  -webkit-animation: drop-122 4.9908947925s 0.8667968157s infinite;
       -o-animation: drop-122 4.9908947925s 0.8667968157s infinite;
          animation: drop-122 4.9908947925s 0.8667968157s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-122 {
  100% {
    top: 110%;
    left: 54%;
  }
}
@-o-keyframes drop-122 {
  100% {
    top: 110%;
    left: 54%;
  }
}
@keyframes drop-122 {
  100% {
    top: 110%;
    left: 54%;
  }
}
.confetti-wrapper .confetti-123 {
  width: 5px;
  height: 2px;
  background-color: #ff9800;
  top: -10%;
  left: 75%;
  opacity: 1.4989005834;
  -webkit-transform: rotate(336.8456266067deg);
      -ms-transform: rotate(336.8456266067deg);
       -o-transform: rotate(336.8456266067deg);
          transform: rotate(336.8456266067deg);
  -webkit-animation: drop-123 4.0246768102s 0.0087364818s infinite;
       -o-animation: drop-123 4.0246768102s 0.0087364818s infinite;
          animation: drop-123 4.0246768102s 0.0087364818s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-123 {
  100% {
    top: 110%;
    left: 82%;
  }
}
@-o-keyframes drop-123 {
  100% {
    top: 110%;
    left: 82%;
  }
}
@keyframes drop-123 {
  100% {
    top: 110%;
    left: 82%;
  }
}
.confetti-wrapper .confetti-124 {
  width: 9px;
  height: 3.6px;
  background-color: #ffc107;
  top: -10%;
  left: 20%;
  opacity: 0.5440202186;
  -webkit-transform: rotate(223.0216567377deg);
      -ms-transform: rotate(223.0216567377deg);
       -o-transform: rotate(223.0216567377deg);
          transform: rotate(223.0216567377deg);
  -webkit-animation: drop-124 4.1161264816s 0.6879706621s infinite;
       -o-animation: drop-124 4.1161264816s 0.6879706621s infinite;
          animation: drop-124 4.1161264816s 0.6879706621s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-124 {
  100% {
    top: 110%;
    left: 29%;
  }
}
@-o-keyframes drop-124 {
  100% {
    top: 110%;
    left: 29%;
  }
}
@keyframes drop-124 {
  100% {
    top: 110%;
    left: 29%;
  }
}
.confetti-wrapper .confetti-125 {
  width: 5px;
  height: 2px;
  background-color: #9c27b0;
  top: -10%;
  left: 60%;
  opacity: 0.9330565758;
  -webkit-transform: rotate(52.7375240584deg);
      -ms-transform: rotate(52.7375240584deg);
       -o-transform: rotate(52.7375240584deg);
          transform: rotate(52.7375240584deg);
  -webkit-animation: drop-125 4.7554207492s 0.6512012791s infinite;
       -o-animation: drop-125 4.7554207492s 0.6512012791s infinite;
          animation: drop-125 4.7554207492s 0.6512012791s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-125 {
  100% {
    top: 110%;
    left: 66%;
  }
}
@-o-keyframes drop-125 {
  100% {
    top: 110%;
    left: 66%;
  }
}
@keyframes drop-125 {
  100% {
    top: 110%;
    left: 66%;
  }
}
.confetti-wrapper .confetti-126 {
  width: 8px;
  height: 3.2px;
  background-color: #C1BB00;
  top: -10%;
  left: 92%;
  opacity: 0.5418844013;
  -webkit-transform: rotate(169.1768706659deg);
      -ms-transform: rotate(169.1768706659deg);
       -o-transform: rotate(169.1768706659deg);
          transform: rotate(169.1768706659deg);
  -webkit-animation: drop-126 4.1261028437s 0.6762937257s infinite;
       -o-animation: drop-126 4.1261028437s 0.6762937257s infinite;
          animation: drop-126 4.1261028437s 0.6762937257s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-126 {
  100% {
    top: 110%;
    left: 104%;
  }
}
@-o-keyframes drop-126 {
  100% {
    top: 110%;
    left: 104%;
  }
}
@keyframes drop-126 {
  100% {
    top: 110%;
    left: 104%;
  }
}
.confetti-wrapper .confetti-127 {
  width: 4px;
  height: 1.6px;
  background-color: #C1BB00;
  top: -10%;
  left: -9%;
  opacity: 1.174981509;
  -webkit-transform: rotate(55.8138176879deg);
      -ms-transform: rotate(55.8138176879deg);
       -o-transform: rotate(55.8138176879deg);
          transform: rotate(55.8138176879deg);
  -webkit-animation: drop-127 4.5195621319s 0.0090431724s infinite;
       -o-animation: drop-127 4.5195621319s 0.0090431724s infinite;
          animation: drop-127 4.5195621319s 0.0090431724s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-127 {
  100% {
    top: 110%;
    left: 1%;
  }
}
@-o-keyframes drop-127 {
  100% {
    top: 110%;
    left: 1%;
  }
}
@keyframes drop-127 {
  100% {
    top: 110%;
    left: 1%;
  }
}
.confetti-wrapper .confetti-128 {
  width: 7px;
  height: 2.8px;
  background-color: #673ab7;
  top: -10%;
  left: 77%;
  opacity: 1.311744958;
  -webkit-transform: rotate(52.6446739deg);
      -ms-transform: rotate(52.6446739deg);
       -o-transform: rotate(52.6446739deg);
          transform: rotate(52.6446739deg);
  -webkit-animation: drop-128 4.0910667171s 0.309093176s infinite;
       -o-animation: drop-128 4.0910667171s 0.309093176s infinite;
          animation: drop-128 4.0910667171s 0.309093176s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-128 {
  100% {
    top: 110%;
    left: 82%;
  }
}
@-o-keyframes drop-128 {
  100% {
    top: 110%;
    left: 82%;
  }
}
@keyframes drop-128 {
  100% {
    top: 110%;
    left: 82%;
  }
}
.confetti-wrapper .confetti-129 {
  width: 5px;
  height: 2px;
  background-color: #C1BB00;
  top: -10%;
  left: 2%;
  opacity: 1.2379208296;
  -webkit-transform: rotate(326.5200484617deg);
      -ms-transform: rotate(326.5200484617deg);
       -o-transform: rotate(326.5200484617deg);
          transform: rotate(326.5200484617deg);
  -webkit-animation: drop-129 4.8041440961s 0.676636998s infinite;
       -o-animation: drop-129 4.8041440961s 0.676636998s infinite;
          animation: drop-129 4.8041440961s 0.676636998s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-129 {
  100% {
    top: 110%;
    left: 3%;
  }
}
@-o-keyframes drop-129 {
  100% {
    top: 110%;
    left: 3%;
  }
}
@keyframes drop-129 {
  100% {
    top: 110%;
    left: 3%;
  }
}
.confetti-wrapper .confetti-130 {
  width: 7px;
  height: 2.8px;
  background-color: #00bcd4;
  top: -10%;
  left: -7%;
  opacity: 1.4906560666;
  -webkit-transform: rotate(123.0825740418deg);
      -ms-transform: rotate(123.0825740418deg);
       -o-transform: rotate(123.0825740418deg);
          transform: rotate(123.0825740418deg);
  -webkit-animation: drop-130 4.0516310517s 0.7768104805s infinite;
       -o-animation: drop-130 4.0516310517s 0.7768104805s infinite;
          animation: drop-130 4.0516310517s 0.7768104805s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-130 {
  100% {
    top: 110%;
    left: -2%;
  }
}
@-o-keyframes drop-130 {
  100% {
    top: 110%;
    left: -2%;
  }
}
@keyframes drop-130 {
  100% {
    top: 110%;
    left: -2%;
  }
}
.confetti-wrapper .confetti-131 {
  width: 6px;
  height: 2.4px;
  background-color: #ff5722;
  top: -10%;
  left: 27%;
  opacity: 0.8664554196;
  -webkit-transform: rotate(314.6945481666deg);
      -ms-transform: rotate(314.6945481666deg);
       -o-transform: rotate(314.6945481666deg);
          transform: rotate(314.6945481666deg);
  -webkit-animation: drop-131 4.9523358875s 0.6498686719s infinite;
       -o-animation: drop-131 4.9523358875s 0.6498686719s infinite;
          animation: drop-131 4.9523358875s 0.6498686719s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-131 {
  100% {
    top: 110%;
    left: 34%;
  }
}
@-o-keyframes drop-131 {
  100% {
    top: 110%;
    left: 34%;
  }
}
@keyframes drop-131 {
  100% {
    top: 110%;
    left: 34%;
  }
}
.confetti-wrapper .confetti-132 {
  width: 7px;
  height: 2.8px;
  background-color: #cddc39;
  top: -10%;
  left: 2%;
  opacity: 0.8547287516;
  -webkit-transform: rotate(278.9215501713deg);
      -ms-transform: rotate(278.9215501713deg);
       -o-transform: rotate(278.9215501713deg);
          transform: rotate(278.9215501713deg);
  -webkit-animation: drop-132 4.2293822347s 0.48529321s infinite;
       -o-animation: drop-132 4.2293822347s 0.48529321s infinite;
          animation: drop-132 4.2293822347s 0.48529321s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-132 {
  100% {
    top: 110%;
    left: 17%;
  }
}
@-o-keyframes drop-132 {
  100% {
    top: 110%;
    left: 17%;
  }
}
@keyframes drop-132 {
  100% {
    top: 110%;
    left: 17%;
  }
}
.confetti-wrapper .confetti-133 {
  width: 4px;
  height: 1.6px;
  background-color: #ff5722;
  top: -10%;
  left: 52%;
  opacity: 0.807378979;
  -webkit-transform: rotate(331.6706079198deg);
      -ms-transform: rotate(331.6706079198deg);
       -o-transform: rotate(331.6706079198deg);
          transform: rotate(331.6706079198deg);
  -webkit-animation: drop-133 4.2800389393s 0.4127323356s infinite;
       -o-animation: drop-133 4.2800389393s 0.4127323356s infinite;
          animation: drop-133 4.2800389393s 0.4127323356s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-133 {
  100% {
    top: 110%;
    left: 66%;
  }
}
@-o-keyframes drop-133 {
  100% {
    top: 110%;
    left: 66%;
  }
}
@keyframes drop-133 {
  100% {
    top: 110%;
    left: 66%;
  }
}
.confetti-wrapper .confetti-134 {
  width: 4px;
  height: 1.6px;
  background-color: #ff5722;
  top: -10%;
  left: 35%;
  opacity: 1.0328699767;
  -webkit-transform: rotate(35.7385437034deg);
      -ms-transform: rotate(35.7385437034deg);
       -o-transform: rotate(35.7385437034deg);
          transform: rotate(35.7385437034deg);
  -webkit-animation: drop-134 4.0101505013s 0.3014980959s infinite;
       -o-animation: drop-134 4.0101505013s 0.3014980959s infinite;
          animation: drop-134 4.0101505013s 0.3014980959s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-134 {
  100% {
    top: 110%;
    left: 38%;
  }
}
@-o-keyframes drop-134 {
  100% {
    top: 110%;
    left: 38%;
  }
}
@keyframes drop-134 {
  100% {
    top: 110%;
    left: 38%;
  }
}
.confetti-wrapper .confetti-135 {
  width: 4px;
  height: 1.6px;
  background-color: #673ab7;
  top: -10%;
  left: 79%;
  opacity: 1.0883650836;
  -webkit-transform: rotate(257.0206017251deg);
      -ms-transform: rotate(257.0206017251deg);
       -o-transform: rotate(257.0206017251deg);
          transform: rotate(257.0206017251deg);
  -webkit-animation: drop-135 4.1786291664s 0.4455789223s infinite;
       -o-animation: drop-135 4.1786291664s 0.4455789223s infinite;
          animation: drop-135 4.1786291664s 0.4455789223s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-135 {
  100% {
    top: 110%;
    left: 82%;
  }
}
@-o-keyframes drop-135 {
  100% {
    top: 110%;
    left: 82%;
  }
}
@keyframes drop-135 {
  100% {
    top: 110%;
    left: 82%;
  }
}
.confetti-wrapper .confetti-136 {
  width: 9px;
  height: 3.6px;
  background-color: #cddc39;
  top: -10%;
  left: 15%;
  opacity: 0.9631206202;
  -webkit-transform: rotate(271.5673137775deg);
      -ms-transform: rotate(271.5673137775deg);
       -o-transform: rotate(271.5673137775deg);
          transform: rotate(271.5673137775deg);
  -webkit-animation: drop-136 4.5167590621s 0.8417546238s infinite;
       -o-animation: drop-136 4.5167590621s 0.8417546238s infinite;
          animation: drop-136 4.5167590621s 0.8417546238s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-136 {
  100% {
    top: 110%;
    left: 21%;
  }
}
@-o-keyframes drop-136 {
  100% {
    top: 110%;
    left: 21%;
  }
}
@keyframes drop-136 {
  100% {
    top: 110%;
    left: 21%;
  }
}
.confetti-wrapper .confetti-137 {
  width: 1px;
  height: 0.4px;
  background-color: #009688;
  top: -10%;
  left: 79%;
  opacity: 1.1973587596;
  -webkit-transform: rotate(47.4421366939deg);
      -ms-transform: rotate(47.4421366939deg);
       -o-transform: rotate(47.4421366939deg);
          transform: rotate(47.4421366939deg);
  -webkit-animation: drop-137 4.5343072259s 0.8648235071s infinite;
       -o-animation: drop-137 4.5343072259s 0.8648235071s infinite;
          animation: drop-137 4.5343072259s 0.8648235071s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-137 {
  100% {
    top: 110%;
    left: 92%;
  }
}
@-o-keyframes drop-137 {
  100% {
    top: 110%;
    left: 92%;
  }
}
@keyframes drop-137 {
  100% {
    top: 110%;
    left: 92%;
  }
}
.confetti-wrapper .confetti-138 {
  width: 4px;
  height: 1.6px;
  background-color: #C1BB00;
  top: -10%;
  left: 58%;
  opacity: 0.7886774543;
  -webkit-transform: rotate(251.0592354741deg);
      -ms-transform: rotate(251.0592354741deg);
       -o-transform: rotate(251.0592354741deg);
          transform: rotate(251.0592354741deg);
  -webkit-animation: drop-138 4.8630364125s 0.5394900909s infinite;
       -o-animation: drop-138 4.8630364125s 0.5394900909s infinite;
          animation: drop-138 4.8630364125s 0.5394900909s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-138 {
  100% {
    top: 110%;
    left: 64%;
  }
}
@-o-keyframes drop-138 {
  100% {
    top: 110%;
    left: 64%;
  }
}
@keyframes drop-138 {
  100% {
    top: 110%;
    left: 64%;
  }
}
.confetti-wrapper .confetti-139 {
  width: 9px;
  height: 3.6px;
  background-color: #03a9f4;
  top: -10%;
  left: 70%;
  opacity: 0.8131450701;
  -webkit-transform: rotate(240.4490794178deg);
      -ms-transform: rotate(240.4490794178deg);
       -o-transform: rotate(240.4490794178deg);
          transform: rotate(240.4490794178deg);
  -webkit-animation: drop-139 4.1902501333s 0.086343473s infinite;
       -o-animation: drop-139 4.1902501333s 0.086343473s infinite;
          animation: drop-139 4.1902501333s 0.086343473s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-139 {
  100% {
    top: 110%;
    left: 73%;
  }
}
@-o-keyframes drop-139 {
  100% {
    top: 110%;
    left: 73%;
  }
}
@keyframes drop-139 {
  100% {
    top: 110%;
    left: 73%;
  }
}
.confetti-wrapper .confetti-140 {
  width: 7px;
  height: 2.8px;
  background-color: #009688;
  top: -10%;
  left: -3%;
  opacity: 1.3323735352;
  -webkit-transform: rotate(20.1763326593deg);
      -ms-transform: rotate(20.1763326593deg);
       -o-transform: rotate(20.1763326593deg);
          transform: rotate(20.1763326593deg);
  -webkit-animation: drop-140 4.5724485264s 0.4627084633s infinite;
       -o-animation: drop-140 4.5724485264s 0.4627084633s infinite;
          animation: drop-140 4.5724485264s 0.4627084633s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-140 {
  100% {
    top: 110%;
    left: 2%;
  }
}
@-o-keyframes drop-140 {
  100% {
    top: 110%;
    left: 2%;
  }
}
@keyframes drop-140 {
  100% {
    top: 110%;
    left: 2%;
  }
}
.confetti-wrapper .confetti-141 {
  width: 5px;
  height: 2px;
  background-color: #F44336;
  top: -10%;
  left: 90%;
  opacity: 0.7106532282;
  -webkit-transform: rotate(59.3118417695deg);
      -ms-transform: rotate(59.3118417695deg);
       -o-transform: rotate(59.3118417695deg);
          transform: rotate(59.3118417695deg);
  -webkit-animation: drop-141 4.5242672683s 0.0068240974s infinite;
       -o-animation: drop-141 4.5242672683s 0.0068240974s infinite;
          animation: drop-141 4.5242672683s 0.0068240974s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-141 {
  100% {
    top: 110%;
    left: 96%;
  }
}
@-o-keyframes drop-141 {
  100% {
    top: 110%;
    left: 96%;
  }
}
@keyframes drop-141 {
  100% {
    top: 110%;
    left: 96%;
  }
}
.confetti-wrapper .confetti-142 {
  width: 10px;
  height: 4px;
  background-color: #03a9f4;
  top: -10%;
  left: 30%;
  opacity: 1.3200754194;
  -webkit-transform: rotate(266.6817947169deg);
      -ms-transform: rotate(266.6817947169deg);
       -o-transform: rotate(266.6817947169deg);
          transform: rotate(266.6817947169deg);
  -webkit-animation: drop-142 4.6342623001s 0.9564742945s infinite;
       -o-animation: drop-142 4.6342623001s 0.9564742945s infinite;
          animation: drop-142 4.6342623001s 0.9564742945s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-142 {
  100% {
    top: 110%;
    left: 40%;
  }
}
@-o-keyframes drop-142 {
  100% {
    top: 110%;
    left: 40%;
  }
}
@keyframes drop-142 {
  100% {
    top: 110%;
    left: 40%;
  }
}
.confetti-wrapper .confetti-143 {
  width: 3px;
  height: 1.2px;
  background-color: #ffc107;
  top: -10%;
  left: -7%;
  opacity: 1.4161257258;
  -webkit-transform: rotate(188.6998298727deg);
      -ms-transform: rotate(188.6998298727deg);
       -o-transform: rotate(188.6998298727deg);
          transform: rotate(188.6998298727deg);
  -webkit-animation: drop-143 4.769249756s 0.4062612256s infinite;
       -o-animation: drop-143 4.769249756s 0.4062612256s infinite;
          animation: drop-143 4.769249756s 0.4062612256s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-143 {
  100% {
    top: 110%;
    left: 5%;
  }
}
@-o-keyframes drop-143 {
  100% {
    top: 110%;
    left: 5%;
  }
}
@keyframes drop-143 {
  100% {
    top: 110%;
    left: 5%;
  }
}
.confetti-wrapper .confetti-144 {
  width: 2px;
  height: 0.8px;
  background-color: #4CAF50;
  top: -10%;
  left: 2%;
  opacity: 0.8530304344;
  -webkit-transform: rotate(358.139758922deg);
      -ms-transform: rotate(358.139758922deg);
       -o-transform: rotate(358.139758922deg);
          transform: rotate(358.139758922deg);
  -webkit-animation: drop-144 4.2660980079s 0.4527408822s infinite;
       -o-animation: drop-144 4.2660980079s 0.4527408822s infinite;
          animation: drop-144 4.2660980079s 0.4527408822s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-144 {
  100% {
    top: 110%;
    left: 3%;
  }
}
@-o-keyframes drop-144 {
  100% {
    top: 110%;
    left: 3%;
  }
}
@keyframes drop-144 {
  100% {
    top: 110%;
    left: 3%;
  }
}
.confetti-wrapper .confetti-145 {
  width: 8px;
  height: 3.2px;
  background-color: #ff5722;
  top: -10%;
  left: 12%;
  opacity: 0.908092192;
  -webkit-transform: rotate(192.0502048635deg);
      -ms-transform: rotate(192.0502048635deg);
       -o-transform: rotate(192.0502048635deg);
          transform: rotate(192.0502048635deg);
  -webkit-animation: drop-145 4.6253656452s 0.7546077863s infinite;
       -o-animation: drop-145 4.6253656452s 0.7546077863s infinite;
          animation: drop-145 4.6253656452s 0.7546077863s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-145 {
  100% {
    top: 110%;
    left: 19%;
  }
}
@-o-keyframes drop-145 {
  100% {
    top: 110%;
    left: 19%;
  }
}
@keyframes drop-145 {
  100% {
    top: 110%;
    left: 19%;
  }
}
.confetti-wrapper .confetti-146 {
  width: 4px;
  height: 1.6px;
  background-color: #cddc39;
  top: -10%;
  left: 39%;
  opacity: 0.7943100313;
  -webkit-transform: rotate(111.7044639126deg);
      -ms-transform: rotate(111.7044639126deg);
       -o-transform: rotate(111.7044639126deg);
          transform: rotate(111.7044639126deg);
  -webkit-animation: drop-146 4.8445317578s 0.8396565028s infinite;
       -o-animation: drop-146 4.8445317578s 0.8396565028s infinite;
          animation: drop-146 4.8445317578s 0.8396565028s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-146 {
  100% {
    top: 110%;
    left: 50%;
  }
}
@-o-keyframes drop-146 {
  100% {
    top: 110%;
    left: 50%;
  }
}
@keyframes drop-146 {
  100% {
    top: 110%;
    left: 50%;
  }
}
.confetti-wrapper .confetti-147 {
  width: 1px;
  height: 0.4px;
  background-color: #4CAF50;
  top: -10%;
  left: 30%;
  opacity: 0.5153047097;
  -webkit-transform: rotate(115.9784072548deg);
      -ms-transform: rotate(115.9784072548deg);
       -o-transform: rotate(115.9784072548deg);
          transform: rotate(115.9784072548deg);
  -webkit-animation: drop-147 4.7105889192s 0.7504247611s infinite;
       -o-animation: drop-147 4.7105889192s 0.7504247611s infinite;
          animation: drop-147 4.7105889192s 0.7504247611s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-147 {
  100% {
    top: 110%;
    left: 44%;
  }
}
@-o-keyframes drop-147 {
  100% {
    top: 110%;
    left: 44%;
  }
}
@keyframes drop-147 {
  100% {
    top: 110%;
    left: 44%;
  }
}
.confetti-wrapper .confetti-148 {
  width: 1px;
  height: 0.4px;
  background-color: #9c27b0;
  top: -10%;
  left: -6%;
  opacity: 1.0192056283;
  -webkit-transform: rotate(65.028699525deg);
      -ms-transform: rotate(65.028699525deg);
       -o-transform: rotate(65.028699525deg);
          transform: rotate(65.028699525deg);
  -webkit-animation: drop-148 4.9147187774s 0.5128957365s infinite;
       -o-animation: drop-148 4.9147187774s 0.5128957365s infinite;
          animation: drop-148 4.9147187774s 0.5128957365s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-148 {
  100% {
    top: 110%;
    left: -5%;
  }
}
@-o-keyframes drop-148 {
  100% {
    top: 110%;
    left: -5%;
  }
}
@keyframes drop-148 {
  100% {
    top: 110%;
    left: -5%;
  }
}
.confetti-wrapper .confetti-149 {
  width: 7px;
  height: 2.8px;
  background-color: #4CAF50;
  top: -10%;
  left: 70%;
  opacity: 0.8964137919;
  -webkit-transform: rotate(326.0770954648deg);
      -ms-transform: rotate(326.0770954648deg);
       -o-transform: rotate(326.0770954648deg);
          transform: rotate(326.0770954648deg);
  -webkit-animation: drop-149 4.1368038215s 0.1939567277s infinite;
       -o-animation: drop-149 4.1368038215s 0.1939567277s infinite;
          animation: drop-149 4.1368038215s 0.1939567277s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-149 {
  100% {
    top: 110%;
    left: 81%;
  }
}
@-o-keyframes drop-149 {
  100% {
    top: 110%;
    left: 81%;
  }
}
@keyframes drop-149 {
  100% {
    top: 110%;
    left: 81%;
  }
}
.confetti-wrapper .confetti-150 {
  width: 7px;
  height: 2.8px;
  background-color: #9c27b0;
  top: -10%;
  left: -3%;
  opacity: 1.4313048339;
  -webkit-transform: rotate(190.1731950533deg);
      -ms-transform: rotate(190.1731950533deg);
       -o-transform: rotate(190.1731950533deg);
          transform: rotate(190.1731950533deg);
  -webkit-animation: drop-150 4.3961936292s 0.98821105s infinite;
       -o-animation: drop-150 4.3961936292s 0.98821105s infinite;
          animation: drop-150 4.3961936292s 0.98821105s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-150 {
  100% {
    top: 110%;
    left: 1%;
  }
}
@-o-keyframes drop-150 {
  100% {
    top: 110%;
    left: 1%;
  }
}
@keyframes drop-150 {
  100% {
    top: 110%;
    left: 1%;
  }
}
.confetti-wrapper .confetti-151 {
  width: 4px;
  height: 1.6px;
  background-color: #4CAF50;
  top: -10%;
  left: 3%;
  opacity: 0.6481387897;
  -webkit-transform: rotate(342.0247046645deg);
      -ms-transform: rotate(342.0247046645deg);
       -o-transform: rotate(342.0247046645deg);
          transform: rotate(342.0247046645deg);
  -webkit-animation: drop-151 4.6935128455s 0.3986948899s infinite;
       -o-animation: drop-151 4.6935128455s 0.3986948899s infinite;
          animation: drop-151 4.6935128455s 0.3986948899s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-151 {
  100% {
    top: 110%;
    left: 18%;
  }
}
@-o-keyframes drop-151 {
  100% {
    top: 110%;
    left: 18%;
  }
}
@keyframes drop-151 {
  100% {
    top: 110%;
    left: 18%;
  }
}
.confetti-wrapper .confetti-152 {
  width: 7px;
  height: 2.8px;
  background-color: #9c27b0;
  top: -10%;
  left: 62%;
  opacity: 1.2522885751;
  -webkit-transform: rotate(90.1070602797deg);
      -ms-transform: rotate(90.1070602797deg);
       -o-transform: rotate(90.1070602797deg);
          transform: rotate(90.1070602797deg);
  -webkit-animation: drop-152 4.3239528921s 0.8395323474s infinite;
       -o-animation: drop-152 4.3239528921s 0.8395323474s infinite;
          animation: drop-152 4.3239528921s 0.8395323474s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-152 {
  100% {
    top: 110%;
    left: 64%;
  }
}
@-o-keyframes drop-152 {
  100% {
    top: 110%;
    left: 64%;
  }
}
@keyframes drop-152 {
  100% {
    top: 110%;
    left: 64%;
  }
}
.confetti-wrapper .confetti-153 {
  width: 9px;
  height: 3.6px;
  background-color: #673ab7;
  top: -10%;
  left: 34%;
  opacity: 0.5760778234;
  -webkit-transform: rotate(255.6981011688deg);
      -ms-transform: rotate(255.6981011688deg);
       -o-transform: rotate(255.6981011688deg);
          transform: rotate(255.6981011688deg);
  -webkit-animation: drop-153 4.8179350457s 0.5188608022s infinite;
       -o-animation: drop-153 4.8179350457s 0.5188608022s infinite;
          animation: drop-153 4.8179350457s 0.5188608022s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-153 {
  100% {
    top: 110%;
    left: 48%;
  }
}
@-o-keyframes drop-153 {
  100% {
    top: 110%;
    left: 48%;
  }
}
@keyframes drop-153 {
  100% {
    top: 110%;
    left: 48%;
  }
}
.confetti-wrapper .confetti-154 {
  width: 7px;
  height: 2.8px;
  background-color: #673ab7;
  top: -10%;
  left: 39%;
  opacity: 0.7651078422;
  -webkit-transform: rotate(266.7933569669deg);
      -ms-transform: rotate(266.7933569669deg);
       -o-transform: rotate(266.7933569669deg);
          transform: rotate(266.7933569669deg);
  -webkit-animation: drop-154 4.8404689027s 0.3157532486s infinite;
       -o-animation: drop-154 4.8404689027s 0.3157532486s infinite;
          animation: drop-154 4.8404689027s 0.3157532486s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-154 {
  100% {
    top: 110%;
    left: 46%;
  }
}
@-o-keyframes drop-154 {
  100% {
    top: 110%;
    left: 46%;
  }
}
@keyframes drop-154 {
  100% {
    top: 110%;
    left: 46%;
  }
}
.confetti-wrapper .confetti-155 {
  width: 1px;
  height: 0.4px;
  background-color: #F44336;
  top: -10%;
  left: 92%;
  opacity: 0.5986753871;
  -webkit-transform: rotate(347.8311352358deg);
      -ms-transform: rotate(347.8311352358deg);
       -o-transform: rotate(347.8311352358deg);
          transform: rotate(347.8311352358deg);
  -webkit-animation: drop-155 4.8489750881s 0.3845316528s infinite;
       -o-animation: drop-155 4.8489750881s 0.3845316528s infinite;
          animation: drop-155 4.8489750881s 0.3845316528s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-155 {
  100% {
    top: 110%;
    left: 99%;
  }
}
@-o-keyframes drop-155 {
  100% {
    top: 110%;
    left: 99%;
  }
}
@keyframes drop-155 {
  100% {
    top: 110%;
    left: 99%;
  }
}
.confetti-wrapper .confetti-156 {
  width: 6px;
  height: 2.4px;
  background-color: #C1BB00;
  top: -10%;
  left: -7%;
  opacity: 0.9720394676;
  -webkit-transform: rotate(243.2586085374deg);
      -ms-transform: rotate(243.2586085374deg);
       -o-transform: rotate(243.2586085374deg);
          transform: rotate(243.2586085374deg);
  -webkit-animation: drop-156 4.0461530536s 0.051801284s infinite;
       -o-animation: drop-156 4.0461530536s 0.051801284s infinite;
          animation: drop-156 4.0461530536s 0.051801284s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-156 {
  100% {
    top: 110%;
    left: -1%;
  }
}
@-o-keyframes drop-156 {
  100% {
    top: 110%;
    left: -1%;
  }
}
@keyframes drop-156 {
  100% {
    top: 110%;
    left: -1%;
  }
}
.confetti-wrapper .confetti-157 {
  width: 2px;
  height: 0.8px;
  background-color: #e91e63;
  top: -10%;
  left: 78%;
  opacity: 1.2557441525;
  -webkit-transform: rotate(5.5445696013deg);
      -ms-transform: rotate(5.5445696013deg);
       -o-transform: rotate(5.5445696013deg);
          transform: rotate(5.5445696013deg);
  -webkit-animation: drop-157 4.9001025133s 0.5105747465s infinite;
       -o-animation: drop-157 4.9001025133s 0.5105747465s infinite;
          animation: drop-157 4.9001025133s 0.5105747465s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-157 {
  100% {
    top: 110%;
    left: 90%;
  }
}
@-o-keyframes drop-157 {
  100% {
    top: 110%;
    left: 90%;
  }
}
@keyframes drop-157 {
  100% {
    top: 110%;
    left: 90%;
  }
}
.confetti-wrapper .confetti-158 {
  width: 7px;
  height: 2.8px;
  background-color: #00bcd4;
  top: -10%;
  left: 10%;
  opacity: 0.7257351425;
  -webkit-transform: rotate(92.2168371666deg);
      -ms-transform: rotate(92.2168371666deg);
       -o-transform: rotate(92.2168371666deg);
          transform: rotate(92.2168371666deg);
  -webkit-animation: drop-158 4.9798147094s 0.757677493s infinite;
       -o-animation: drop-158 4.9798147094s 0.757677493s infinite;
          animation: drop-158 4.9798147094s 0.757677493s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-158 {
  100% {
    top: 110%;
    left: 17%;
  }
}
@-o-keyframes drop-158 {
  100% {
    top: 110%;
    left: 17%;
  }
}
@keyframes drop-158 {
  100% {
    top: 110%;
    left: 17%;
  }
}
.confetti-wrapper .confetti-159 {
  width: 1px;
  height: 0.4px;
  background-color: #00bcd4;
  top: -10%;
  left: 80%;
  opacity: 1.2142731887;
  -webkit-transform: rotate(289.6538998759deg);
      -ms-transform: rotate(289.6538998759deg);
       -o-transform: rotate(289.6538998759deg);
          transform: rotate(289.6538998759deg);
  -webkit-animation: drop-159 4.7162959336s 0.856156186s infinite;
       -o-animation: drop-159 4.7162959336s 0.856156186s infinite;
          animation: drop-159 4.7162959336s 0.856156186s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-159 {
  100% {
    top: 110%;
    left: 85%;
  }
}
@-o-keyframes drop-159 {
  100% {
    top: 110%;
    left: 85%;
  }
}
@keyframes drop-159 {
  100% {
    top: 110%;
    left: 85%;
  }
}
.confetti-wrapper .confetti-160 {
  width: 5px;
  height: 2px;
  background-color: #e91e63;
  top: -10%;
  left: 67%;
  opacity: 0.6386348602;
  -webkit-transform: rotate(28.5313285354deg);
      -ms-transform: rotate(28.5313285354deg);
       -o-transform: rotate(28.5313285354deg);
          transform: rotate(28.5313285354deg);
  -webkit-animation: drop-160 4.4698135031s 0.6029838279s infinite;
       -o-animation: drop-160 4.4698135031s 0.6029838279s infinite;
          animation: drop-160 4.4698135031s 0.6029838279s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-160 {
  100% {
    top: 110%;
    left: 80%;
  }
}
@-o-keyframes drop-160 {
  100% {
    top: 110%;
    left: 80%;
  }
}
@keyframes drop-160 {
  100% {
    top: 110%;
    left: 80%;
  }
}
.confetti-wrapper .confetti-161 {
  width: 5px;
  height: 2px;
  background-color: #009688;
  top: -10%;
  left: 24%;
  opacity: 0.9360182356;
  -webkit-transform: rotate(269.4831849069deg);
      -ms-transform: rotate(269.4831849069deg);
       -o-transform: rotate(269.4831849069deg);
          transform: rotate(269.4831849069deg);
  -webkit-animation: drop-161 4.913822201s 0.0111637509s infinite;
       -o-animation: drop-161 4.913822201s 0.0111637509s infinite;
          animation: drop-161 4.913822201s 0.0111637509s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-161 {
  100% {
    top: 110%;
    left: 31%;
  }
}
@-o-keyframes drop-161 {
  100% {
    top: 110%;
    left: 31%;
  }
}
@keyframes drop-161 {
  100% {
    top: 110%;
    left: 31%;
  }
}
.confetti-wrapper .confetti-162 {
  width: 9px;
  height: 3.6px;
  background-color: #03a9f4;
  top: -10%;
  left: 58%;
  opacity: 0.6897425669;
  -webkit-transform: rotate(218.8964417789deg);
      -ms-transform: rotate(218.8964417789deg);
       -o-transform: rotate(218.8964417789deg);
          transform: rotate(218.8964417789deg);
  -webkit-animation: drop-162 4.5808785258s 0.3383102834s infinite;
       -o-animation: drop-162 4.5808785258s 0.3383102834s infinite;
          animation: drop-162 4.5808785258s 0.3383102834s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-162 {
  100% {
    top: 110%;
    left: 69%;
  }
}
@-o-keyframes drop-162 {
  100% {
    top: 110%;
    left: 69%;
  }
}
@keyframes drop-162 {
  100% {
    top: 110%;
    left: 69%;
  }
}
.confetti-wrapper .confetti-163 {
  width: 1px;
  height: 0.4px;
  background-color: #4CAF50;
  top: -10%;
  left: -5%;
  opacity: 0.8968516619;
  -webkit-transform: rotate(100.9243871949deg);
      -ms-transform: rotate(100.9243871949deg);
       -o-transform: rotate(100.9243871949deg);
          transform: rotate(100.9243871949deg);
  -webkit-animation: drop-163 4.6720370717s 0.7928798353s infinite;
       -o-animation: drop-163 4.6720370717s 0.7928798353s infinite;
          animation: drop-163 4.6720370717s 0.7928798353s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-163 {
  100% {
    top: 110%;
    left: 2%;
  }
}
@-o-keyframes drop-163 {
  100% {
    top: 110%;
    left: 2%;
  }
}
@keyframes drop-163 {
  100% {
    top: 110%;
    left: 2%;
  }
}
.confetti-wrapper .confetti-164 {
  width: 10px;
  height: 4px;
  background-color: #03a9f4;
  top: -10%;
  left: 12%;
  opacity: 1.3651516022;
  -webkit-transform: rotate(76.6989355993deg);
      -ms-transform: rotate(76.6989355993deg);
       -o-transform: rotate(76.6989355993deg);
          transform: rotate(76.6989355993deg);
  -webkit-animation: drop-164 4.1304552306s 0.0340508369s infinite;
       -o-animation: drop-164 4.1304552306s 0.0340508369s infinite;
          animation: drop-164 4.1304552306s 0.0340508369s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-164 {
  100% {
    top: 110%;
    left: 24%;
  }
}
@-o-keyframes drop-164 {
  100% {
    top: 110%;
    left: 24%;
  }
}
@keyframes drop-164 {
  100% {
    top: 110%;
    left: 24%;
  }
}
.confetti-wrapper .confetti-165 {
  width: 8px;
  height: 3.2px;
  background-color: #3f51b5;
  top: -10%;
  left: 72%;
  opacity: 0.5435396938;
  -webkit-transform: rotate(285.2534673436deg);
      -ms-transform: rotate(285.2534673436deg);
       -o-transform: rotate(285.2534673436deg);
          transform: rotate(285.2534673436deg);
  -webkit-animation: drop-165 4.1363277301s 0.4193452016s infinite;
       -o-animation: drop-165 4.1363277301s 0.4193452016s infinite;
          animation: drop-165 4.1363277301s 0.4193452016s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-165 {
  100% {
    top: 110%;
    left: 74%;
  }
}
@-o-keyframes drop-165 {
  100% {
    top: 110%;
    left: 74%;
  }
}
@keyframes drop-165 {
  100% {
    top: 110%;
    left: 74%;
  }
}
.confetti-wrapper .confetti-166 {
  width: 6px;
  height: 2.4px;
  background-color: #ffc107;
  top: -10%;
  left: 61%;
  opacity: 1.4117367463;
  -webkit-transform: rotate(223.0298697943deg);
      -ms-transform: rotate(223.0298697943deg);
       -o-transform: rotate(223.0298697943deg);
          transform: rotate(223.0298697943deg);
  -webkit-animation: drop-166 4.1913507315s 0.6134736777s infinite;
       -o-animation: drop-166 4.1913507315s 0.6134736777s infinite;
          animation: drop-166 4.1913507315s 0.6134736777s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-166 {
  100% {
    top: 110%;
    left: 75%;
  }
}
@-o-keyframes drop-166 {
  100% {
    top: 110%;
    left: 75%;
  }
}
@keyframes drop-166 {
  100% {
    top: 110%;
    left: 75%;
  }
}
.confetti-wrapper .confetti-167 {
  width: 8px;
  height: 3.2px;
  background-color: #e91e63;
  top: -10%;
  left: 86%;
  opacity: 1.4238631057;
  -webkit-transform: rotate(345.8932562833deg);
      -ms-transform: rotate(345.8932562833deg);
       -o-transform: rotate(345.8932562833deg);
          transform: rotate(345.8932562833deg);
  -webkit-animation: drop-167 4.7384293845s 0.7540743993s infinite;
       -o-animation: drop-167 4.7384293845s 0.7540743993s infinite;
          animation: drop-167 4.7384293845s 0.7540743993s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-167 {
  100% {
    top: 110%;
    left: 98%;
  }
}
@-o-keyframes drop-167 {
  100% {
    top: 110%;
    left: 98%;
  }
}
@keyframes drop-167 {
  100% {
    top: 110%;
    left: 98%;
  }
}
.confetti-wrapper .confetti-168 {
  width: 9px;
  height: 3.6px;
  background-color: #673ab7;
  top: -10%;
  left: 90%;
  opacity: 1.0384797686;
  -webkit-transform: rotate(15.3355302326deg);
      -ms-transform: rotate(15.3355302326deg);
       -o-transform: rotate(15.3355302326deg);
          transform: rotate(15.3355302326deg);
  -webkit-animation: drop-168 4.7297410043s 0.2937175012s infinite;
       -o-animation: drop-168 4.7297410043s 0.2937175012s infinite;
          animation: drop-168 4.7297410043s 0.2937175012s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-168 {
  100% {
    top: 110%;
    left: 93%;
  }
}
@-o-keyframes drop-168 {
  100% {
    top: 110%;
    left: 93%;
  }
}
@keyframes drop-168 {
  100% {
    top: 110%;
    left: 93%;
  }
}
.confetti-wrapper .confetti-169 {
  width: 2px;
  height: 0.8px;
  background-color: #C1BB00;
  top: -10%;
  left: 33%;
  opacity: 0.9388164343;
  -webkit-transform: rotate(24.0800512871deg);
      -ms-transform: rotate(24.0800512871deg);
       -o-transform: rotate(24.0800512871deg);
          transform: rotate(24.0800512871deg);
  -webkit-animation: drop-169 4.1150041705s 0.8189814976s infinite;
       -o-animation: drop-169 4.1150041705s 0.8189814976s infinite;
          animation: drop-169 4.1150041705s 0.8189814976s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-169 {
  100% {
    top: 110%;
    left: 34%;
  }
}
@-o-keyframes drop-169 {
  100% {
    top: 110%;
    left: 34%;
  }
}
@keyframes drop-169 {
  100% {
    top: 110%;
    left: 34%;
  }
}
.confetti-wrapper .confetti-170 {
  width: 4px;
  height: 1.6px;
  background-color: #4CAF50;
  top: -10%;
  left: -5%;
  opacity: 1.0585334072;
  -webkit-transform: rotate(44.7546107635deg);
      -ms-transform: rotate(44.7546107635deg);
       -o-transform: rotate(44.7546107635deg);
          transform: rotate(44.7546107635deg);
  -webkit-animation: drop-170 4.3149069258s 0.2345777095s infinite;
       -o-animation: drop-170 4.3149069258s 0.2345777095s infinite;
          animation: drop-170 4.3149069258s 0.2345777095s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-170 {
  100% {
    top: 110%;
    left: -4%;
  }
}
@-o-keyframes drop-170 {
  100% {
    top: 110%;
    left: -4%;
  }
}
@keyframes drop-170 {
  100% {
    top: 110%;
    left: -4%;
  }
}
.confetti-wrapper .confetti-171 {
  width: 7px;
  height: 2.8px;
  background-color: #ff9800;
  top: -10%;
  left: -2%;
  opacity: 1.4419656634;
  -webkit-transform: rotate(32.3820755564deg);
      -ms-transform: rotate(32.3820755564deg);
       -o-transform: rotate(32.3820755564deg);
          transform: rotate(32.3820755564deg);
  -webkit-animation: drop-171 4.0622208015s 0.3918152436s infinite;
       -o-animation: drop-171 4.0622208015s 0.3918152436s infinite;
          animation: drop-171 4.0622208015s 0.3918152436s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-171 {
  100% {
    top: 110%;
    left: 1%;
  }
}
@-o-keyframes drop-171 {
  100% {
    top: 110%;
    left: 1%;
  }
}
@keyframes drop-171 {
  100% {
    top: 110%;
    left: 1%;
  }
}
.confetti-wrapper .confetti-172 {
  width: 6px;
  height: 2.4px;
  background-color: #cddc39;
  top: -10%;
  left: 54%;
  opacity: 1.4785465312;
  -webkit-transform: rotate(174.702023018deg);
      -ms-transform: rotate(174.702023018deg);
       -o-transform: rotate(174.702023018deg);
          transform: rotate(174.702023018deg);
  -webkit-animation: drop-172 4.8563092191s 0.0182621049s infinite;
       -o-animation: drop-172 4.8563092191s 0.0182621049s infinite;
          animation: drop-172 4.8563092191s 0.0182621049s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-172 {
  100% {
    top: 110%;
    left: 62%;
  }
}
@-o-keyframes drop-172 {
  100% {
    top: 110%;
    left: 62%;
  }
}
@keyframes drop-172 {
  100% {
    top: 110%;
    left: 62%;
  }
}
.confetti-wrapper .confetti-173 {
  width: 6px;
  height: 2.4px;
  background-color: #C1BB00;
  top: -10%;
  left: 15%;
  opacity: 0.8634915598;
  -webkit-transform: rotate(128.2202686354deg);
      -ms-transform: rotate(128.2202686354deg);
       -o-transform: rotate(128.2202686354deg);
          transform: rotate(128.2202686354deg);
  -webkit-animation: drop-173 4.990728853s 0.3647418003s infinite;
       -o-animation: drop-173 4.990728853s 0.3647418003s infinite;
          animation: drop-173 4.990728853s 0.3647418003s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-173 {
  100% {
    top: 110%;
    left: 17%;
  }
}
@-o-keyframes drop-173 {
  100% {
    top: 110%;
    left: 17%;
  }
}
@keyframes drop-173 {
  100% {
    top: 110%;
    left: 17%;
  }
}
.confetti-wrapper .confetti-174 {
  width: 5px;
  height: 2px;
  background-color: #F44336;
  top: -10%;
  left: 42%;
  opacity: 1.0090282504;
  -webkit-transform: rotate(222.0405202101deg);
      -ms-transform: rotate(222.0405202101deg);
       -o-transform: rotate(222.0405202101deg);
          transform: rotate(222.0405202101deg);
  -webkit-animation: drop-174 4.0954697169s 0.9013751016s infinite;
       -o-animation: drop-174 4.0954697169s 0.9013751016s infinite;
          animation: drop-174 4.0954697169s 0.9013751016s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-174 {
  100% {
    top: 110%;
    left: 57%;
  }
}
@-o-keyframes drop-174 {
  100% {
    top: 110%;
    left: 57%;
  }
}
@keyframes drop-174 {
  100% {
    top: 110%;
    left: 57%;
  }
}
.confetti-wrapper .confetti-175 {
  width: 6px;
  height: 2.4px;
  background-color: #673ab7;
  top: -10%;
  left: 54%;
  opacity: 1.081157728;
  -webkit-transform: rotate(68.4334042006deg);
      -ms-transform: rotate(68.4334042006deg);
       -o-transform: rotate(68.4334042006deg);
          transform: rotate(68.4334042006deg);
  -webkit-animation: drop-175 4.2912096898s 0.306233192s infinite;
       -o-animation: drop-175 4.2912096898s 0.306233192s infinite;
          animation: drop-175 4.2912096898s 0.306233192s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-175 {
  100% {
    top: 110%;
    left: 64%;
  }
}
@-o-keyframes drop-175 {
  100% {
    top: 110%;
    left: 64%;
  }
}
@keyframes drop-175 {
  100% {
    top: 110%;
    left: 64%;
  }
}
.confetti-wrapper .confetti-176 {
  width: 6px;
  height: 2.4px;
  background-color: #00bcd4;
  top: -10%;
  left: 8%;
  opacity: 1.1102691418;
  -webkit-transform: rotate(215.5251668879deg);
      -ms-transform: rotate(215.5251668879deg);
       -o-transform: rotate(215.5251668879deg);
          transform: rotate(215.5251668879deg);
  -webkit-animation: drop-176 4.5855657536s 0.2353644599s infinite;
       -o-animation: drop-176 4.5855657536s 0.2353644599s infinite;
          animation: drop-176 4.5855657536s 0.2353644599s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-176 {
  100% {
    top: 110%;
    left: 19%;
  }
}
@-o-keyframes drop-176 {
  100% {
    top: 110%;
    left: 19%;
  }
}
@keyframes drop-176 {
  100% {
    top: 110%;
    left: 19%;
  }
}
.confetti-wrapper .confetti-177 {
  width: 4px;
  height: 1.6px;
  background-color: #673ab7;
  top: -10%;
  left: 1%;
  opacity: 1.0898164856;
  -webkit-transform: rotate(141.2184248752deg);
      -ms-transform: rotate(141.2184248752deg);
       -o-transform: rotate(141.2184248752deg);
          transform: rotate(141.2184248752deg);
  -webkit-animation: drop-177 4.5645454387s 0.7416676624s infinite;
       -o-animation: drop-177 4.5645454387s 0.7416676624s infinite;
          animation: drop-177 4.5645454387s 0.7416676624s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-177 {
  100% {
    top: 110%;
    left: 12%;
  }
}
@-o-keyframes drop-177 {
  100% {
    top: 110%;
    left: 12%;
  }
}
@keyframes drop-177 {
  100% {
    top: 110%;
    left: 12%;
  }
}
.confetti-wrapper .confetti-178 {
  width: 3px;
  height: 1.2px;
  background-color: #8bc34a;
  top: -10%;
  left: 56%;
  opacity: 0.7607578269;
  -webkit-transform: rotate(158.2074409596deg);
      -ms-transform: rotate(158.2074409596deg);
       -o-transform: rotate(158.2074409596deg);
          transform: rotate(158.2074409596deg);
  -webkit-animation: drop-178 4.4316213561s 0.5817274498s infinite;
       -o-animation: drop-178 4.4316213561s 0.5817274498s infinite;
          animation: drop-178 4.4316213561s 0.5817274498s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-178 {
  100% {
    top: 110%;
    left: 64%;
  }
}
@-o-keyframes drop-178 {
  100% {
    top: 110%;
    left: 64%;
  }
}
@keyframes drop-178 {
  100% {
    top: 110%;
    left: 64%;
  }
}
.confetti-wrapper .confetti-179 {
  width: 3px;
  height: 1.2px;
  background-color: #009688;
  top: -10%;
  left: -8%;
  opacity: 0.943157005;
  -webkit-transform: rotate(168.1425544295deg);
      -ms-transform: rotate(168.1425544295deg);
       -o-transform: rotate(168.1425544295deg);
          transform: rotate(168.1425544295deg);
  -webkit-animation: drop-179 4.7386459987s 0.8390019072s infinite;
       -o-animation: drop-179 4.7386459987s 0.8390019072s infinite;
          animation: drop-179 4.7386459987s 0.8390019072s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-179 {
  100% {
    top: 110%;
    left: 3%;
  }
}
@-o-keyframes drop-179 {
  100% {
    top: 110%;
    left: 3%;
  }
}
@keyframes drop-179 {
  100% {
    top: 110%;
    left: 3%;
  }
}
.confetti-wrapper .confetti-180 {
  width: 2px;
  height: 0.8px;
  background-color: #cddc39;
  top: -10%;
  left: 57%;
  opacity: 1.1600075787;
  -webkit-transform: rotate(164.0822442193deg);
      -ms-transform: rotate(164.0822442193deg);
       -o-transform: rotate(164.0822442193deg);
          transform: rotate(164.0822442193deg);
  -webkit-animation: drop-180 4.8909278178s 0.2937397787s infinite;
       -o-animation: drop-180 4.8909278178s 0.2937397787s infinite;
          animation: drop-180 4.8909278178s 0.2937397787s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-180 {
  100% {
    top: 110%;
    left: 63%;
  }
}
@-o-keyframes drop-180 {
  100% {
    top: 110%;
    left: 63%;
  }
}
@keyframes drop-180 {
  100% {
    top: 110%;
    left: 63%;
  }
}
.confetti-wrapper .confetti-181 {
  width: 5px;
  height: 2px;
  background-color: #e91e63;
  top: -10%;
  left: 54%;
  opacity: 0.6208607806;
  -webkit-transform: rotate(16.5865238907deg);
      -ms-transform: rotate(16.5865238907deg);
       -o-transform: rotate(16.5865238907deg);
          transform: rotate(16.5865238907deg);
  -webkit-animation: drop-181 4.5777670773s 0.0611831655s infinite;
       -o-animation: drop-181 4.5777670773s 0.0611831655s infinite;
          animation: drop-181 4.5777670773s 0.0611831655s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-181 {
  100% {
    top: 110%;
    left: 68%;
  }
}
@-o-keyframes drop-181 {
  100% {
    top: 110%;
    left: 68%;
  }
}
@keyframes drop-181 {
  100% {
    top: 110%;
    left: 68%;
  }
}
.confetti-wrapper .confetti-182 {
  width: 5px;
  height: 2px;
  background-color: #00bcd4;
  top: -10%;
  left: 98%;
  opacity: 1.1524552472;
  -webkit-transform: rotate(281.6821510271deg);
      -ms-transform: rotate(281.6821510271deg);
       -o-transform: rotate(281.6821510271deg);
          transform: rotate(281.6821510271deg);
  -webkit-animation: drop-182 4.9859992951s 0.1125500393s infinite;
       -o-animation: drop-182 4.9859992951s 0.1125500393s infinite;
          animation: drop-182 4.9859992951s 0.1125500393s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-182 {
  100% {
    top: 110%;
    left: 102%;
  }
}
@-o-keyframes drop-182 {
  100% {
    top: 110%;
    left: 102%;
  }
}
@keyframes drop-182 {
  100% {
    top: 110%;
    left: 102%;
  }
}
.confetti-wrapper .confetti-183 {
  width: 1px;
  height: 0.4px;
  background-color: #ffc107;
  top: -10%;
  left: 37%;
  opacity: 1.4093865686;
  -webkit-transform: rotate(60.0783952952deg);
      -ms-transform: rotate(60.0783952952deg);
       -o-transform: rotate(60.0783952952deg);
          transform: rotate(60.0783952952deg);
  -webkit-animation: drop-183 4.9845315509s 0.8695625105s infinite;
       -o-animation: drop-183 4.9845315509s 0.8695625105s infinite;
          animation: drop-183 4.9845315509s 0.8695625105s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-183 {
  100% {
    top: 110%;
    left: 51%;
  }
}
@-o-keyframes drop-183 {
  100% {
    top: 110%;
    left: 51%;
  }
}
@keyframes drop-183 {
  100% {
    top: 110%;
    left: 51%;
  }
}
.confetti-wrapper .confetti-184 {
  width: 10px;
  height: 4px;
  background-color: #00bcd4;
  top: -10%;
  left: 47%;
  opacity: 1.0312802048;
  -webkit-transform: rotate(293.5678699439deg);
      -ms-transform: rotate(293.5678699439deg);
       -o-transform: rotate(293.5678699439deg);
          transform: rotate(293.5678699439deg);
  -webkit-animation: drop-184 4.1694884839s 0.1734458021s infinite;
       -o-animation: drop-184 4.1694884839s 0.1734458021s infinite;
          animation: drop-184 4.1694884839s 0.1734458021s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-184 {
  100% {
    top: 110%;
    left: 49%;
  }
}
@-o-keyframes drop-184 {
  100% {
    top: 110%;
    left: 49%;
  }
}
@keyframes drop-184 {
  100% {
    top: 110%;
    left: 49%;
  }
}
.confetti-wrapper .confetti-185 {
  width: 2px;
  height: 0.8px;
  background-color: #ff9800;
  top: -10%;
  left: 63%;
  opacity: 1.2817965523;
  -webkit-transform: rotate(225.3831706056deg);
      -ms-transform: rotate(225.3831706056deg);
       -o-transform: rotate(225.3831706056deg);
          transform: rotate(225.3831706056deg);
  -webkit-animation: drop-185 4.2632314167s 0.9165834233s infinite;
       -o-animation: drop-185 4.2632314167s 0.9165834233s infinite;
          animation: drop-185 4.2632314167s 0.9165834233s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-185 {
  100% {
    top: 110%;
    left: 64%;
  }
}
@-o-keyframes drop-185 {
  100% {
    top: 110%;
    left: 64%;
  }
}
@keyframes drop-185 {
  100% {
    top: 110%;
    left: 64%;
  }
}
.confetti-wrapper .confetti-186 {
  width: 10px;
  height: 4px;
  background-color: #673ab7;
  top: -10%;
  left: 100%;
  opacity: 1.4345479805;
  -webkit-transform: rotate(286.9958533901deg);
      -ms-transform: rotate(286.9958533901deg);
       -o-transform: rotate(286.9958533901deg);
          transform: rotate(286.9958533901deg);
  -webkit-animation: drop-186 4.3428147384s 0.7526860663s infinite;
       -o-animation: drop-186 4.3428147384s 0.7526860663s infinite;
          animation: drop-186 4.3428147384s 0.7526860663s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-186 {
  100% {
    top: 110%;
    left: 114%;
  }
}
@-o-keyframes drop-186 {
  100% {
    top: 110%;
    left: 114%;
  }
}
@keyframes drop-186 {
  100% {
    top: 110%;
    left: 114%;
  }
}
.confetti-wrapper .confetti-187 {
  width: 2px;
  height: 0.8px;
  background-color: #ff5722;
  top: -10%;
  left: -2%;
  opacity: 0.6714712384;
  -webkit-transform: rotate(42.2101494052deg);
      -ms-transform: rotate(42.2101494052deg);
       -o-transform: rotate(42.2101494052deg);
          transform: rotate(42.2101494052deg);
  -webkit-animation: drop-187 4.2748032155s 0.9307707204s infinite;
       -o-animation: drop-187 4.2748032155s 0.9307707204s infinite;
          animation: drop-187 4.2748032155s 0.9307707204s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-187 {
  100% {
    top: 110%;
    left: 8%;
  }
}
@-o-keyframes drop-187 {
  100% {
    top: 110%;
    left: 8%;
  }
}
@keyframes drop-187 {
  100% {
    top: 110%;
    left: 8%;
  }
}
.confetti-wrapper .confetti-188 {
  width: 4px;
  height: 1.6px;
  background-color: #00bcd4;
  top: -10%;
  left: 16%;
  opacity: 1.3144999708;
  -webkit-transform: rotate(98.3169031858deg);
      -ms-transform: rotate(98.3169031858deg);
       -o-transform: rotate(98.3169031858deg);
          transform: rotate(98.3169031858deg);
  -webkit-animation: drop-188 4.3819589562s 0.3245069344s infinite;
       -o-animation: drop-188 4.3819589562s 0.3245069344s infinite;
          animation: drop-188 4.3819589562s 0.3245069344s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-188 {
  100% {
    top: 110%;
    left: 19%;
  }
}
@-o-keyframes drop-188 {
  100% {
    top: 110%;
    left: 19%;
  }
}
@keyframes drop-188 {
  100% {
    top: 110%;
    left: 19%;
  }
}
.confetti-wrapper .confetti-189 {
  width: 6px;
  height: 2.4px;
  background-color: #cddc39;
  top: -10%;
  left: 32%;
  opacity: 1.4590272883;
  -webkit-transform: rotate(160.7189602987deg);
      -ms-transform: rotate(160.7189602987deg);
       -o-transform: rotate(160.7189602987deg);
          transform: rotate(160.7189602987deg);
  -webkit-animation: drop-189 4.9752486341s 0.6426575405s infinite;
       -o-animation: drop-189 4.9752486341s 0.6426575405s infinite;
          animation: drop-189 4.9752486341s 0.6426575405s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-189 {
  100% {
    top: 110%;
    left: 39%;
  }
}
@-o-keyframes drop-189 {
  100% {
    top: 110%;
    left: 39%;
  }
}
@keyframes drop-189 {
  100% {
    top: 110%;
    left: 39%;
  }
}
.confetti-wrapper .confetti-190 {
  width: 5px;
  height: 2px;
  background-color: #00bcd4;
  top: -10%;
  left: 67%;
  opacity: 0.6271790931;
  -webkit-transform: rotate(307.8386543608deg);
      -ms-transform: rotate(307.8386543608deg);
       -o-transform: rotate(307.8386543608deg);
          transform: rotate(307.8386543608deg);
  -webkit-animation: drop-190 4.1023287626s 0.3026743595s infinite;
       -o-animation: drop-190 4.1023287626s 0.3026743595s infinite;
          animation: drop-190 4.1023287626s 0.3026743595s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-190 {
  100% {
    top: 110%;
    left: 78%;
  }
}
@-o-keyframes drop-190 {
  100% {
    top: 110%;
    left: 78%;
  }
}
@keyframes drop-190 {
  100% {
    top: 110%;
    left: 78%;
  }
}
.confetti-wrapper .confetti-191 {
  width: 9px;
  height: 3.6px;
  background-color: #C1BB00;
  top: -10%;
  left: -8%;
  opacity: 1.0792618165;
  -webkit-transform: rotate(107.1062451352deg);
      -ms-transform: rotate(107.1062451352deg);
       -o-transform: rotate(107.1062451352deg);
          transform: rotate(107.1062451352deg);
  -webkit-animation: drop-191 4.0086737696s 0.8650744648s infinite;
       -o-animation: drop-191 4.0086737696s 0.8650744648s infinite;
          animation: drop-191 4.0086737696s 0.8650744648s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-191 {
  100% {
    top: 110%;
    left: -4%;
  }
}
@-o-keyframes drop-191 {
  100% {
    top: 110%;
    left: -4%;
  }
}
@keyframes drop-191 {
  100% {
    top: 110%;
    left: -4%;
  }
}
.confetti-wrapper .confetti-192 {
  width: 10px;
  height: 4px;
  background-color: #4CAF50;
  top: -10%;
  left: 76%;
  opacity: 1.4956312672;
  -webkit-transform: rotate(171.7600000329deg);
      -ms-transform: rotate(171.7600000329deg);
       -o-transform: rotate(171.7600000329deg);
          transform: rotate(171.7600000329deg);
  -webkit-animation: drop-192 4.0385200442s 0.9415056123s infinite;
       -o-animation: drop-192 4.0385200442s 0.9415056123s infinite;
          animation: drop-192 4.0385200442s 0.9415056123s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-192 {
  100% {
    top: 110%;
    left: 78%;
  }
}
@-o-keyframes drop-192 {
  100% {
    top: 110%;
    left: 78%;
  }
}
@keyframes drop-192 {
  100% {
    top: 110%;
    left: 78%;
  }
}
.confetti-wrapper .confetti-193 {
  width: 10px;
  height: 4px;
  background-color: #03a9f4;
  top: -10%;
  left: 46%;
  opacity: 1.0542343759;
  -webkit-transform: rotate(322.5981205087deg);
      -ms-transform: rotate(322.5981205087deg);
       -o-transform: rotate(322.5981205087deg);
          transform: rotate(322.5981205087deg);
  -webkit-animation: drop-193 4.1014673046s 0.8399249518s infinite;
       -o-animation: drop-193 4.1014673046s 0.8399249518s infinite;
          animation: drop-193 4.1014673046s 0.8399249518s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-193 {
  100% {
    top: 110%;
    left: 47%;
  }
}
@-o-keyframes drop-193 {
  100% {
    top: 110%;
    left: 47%;
  }
}
@keyframes drop-193 {
  100% {
    top: 110%;
    left: 47%;
  }
}
.confetti-wrapper .confetti-194 {
  width: 8px;
  height: 3.2px;
  background-color: #673ab7;
  top: -10%;
  left: 57%;
  opacity: 1.0279335618;
  -webkit-transform: rotate(130.5306626529deg);
      -ms-transform: rotate(130.5306626529deg);
       -o-transform: rotate(130.5306626529deg);
          transform: rotate(130.5306626529deg);
  -webkit-animation: drop-194 4.8733974716s 0.785320555s infinite;
       -o-animation: drop-194 4.8733974716s 0.785320555s infinite;
          animation: drop-194 4.8733974716s 0.785320555s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-194 {
  100% {
    top: 110%;
    left: 64%;
  }
}
@-o-keyframes drop-194 {
  100% {
    top: 110%;
    left: 64%;
  }
}
@keyframes drop-194 {
  100% {
    top: 110%;
    left: 64%;
  }
}
.confetti-wrapper .confetti-195 {
  width: 6px;
  height: 2.4px;
  background-color: #ffc107;
  top: -10%;
  left: 32%;
  opacity: 0.6160627208;
  -webkit-transform: rotate(272.8934411108deg);
      -ms-transform: rotate(272.8934411108deg);
       -o-transform: rotate(272.8934411108deg);
          transform: rotate(272.8934411108deg);
  -webkit-animation: drop-195 4.602278265s 0.7719966907s infinite;
       -o-animation: drop-195 4.602278265s 0.7719966907s infinite;
          animation: drop-195 4.602278265s 0.7719966907s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-195 {
  100% {
    top: 110%;
    left: 37%;
  }
}
@-o-keyframes drop-195 {
  100% {
    top: 110%;
    left: 37%;
  }
}
@keyframes drop-195 {
  100% {
    top: 110%;
    left: 37%;
  }
}
.confetti-wrapper .confetti-196 {
  width: 4px;
  height: 1.6px;
  background-color: #cddc39;
  top: -10%;
  left: 97%;
  opacity: 0.9880442551;
  -webkit-transform: rotate(225.9724801492deg);
      -ms-transform: rotate(225.9724801492deg);
       -o-transform: rotate(225.9724801492deg);
          transform: rotate(225.9724801492deg);
  -webkit-animation: drop-196 4.4167371648s 0.0409255924s infinite;
       -o-animation: drop-196 4.4167371648s 0.0409255924s infinite;
          animation: drop-196 4.4167371648s 0.0409255924s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-196 {
  100% {
    top: 110%;
    left: 107%;
  }
}
@-o-keyframes drop-196 {
  100% {
    top: 110%;
    left: 107%;
  }
}
@keyframes drop-196 {
  100% {
    top: 110%;
    left: 107%;
  }
}
.confetti-wrapper .confetti-197 {
  width: 8px;
  height: 3.2px;
  background-color: #F44336;
  top: -10%;
  left: 89%;
  opacity: 0.9814385795;
  -webkit-transform: rotate(279.2066585123deg);
      -ms-transform: rotate(279.2066585123deg);
       -o-transform: rotate(279.2066585123deg);
          transform: rotate(279.2066585123deg);
  -webkit-animation: drop-197 4.2998273305s 0.9814944794s infinite;
       -o-animation: drop-197 4.2998273305s 0.9814944794s infinite;
          animation: drop-197 4.2998273305s 0.9814944794s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-197 {
  100% {
    top: 110%;
    left: 100%;
  }
}
@-o-keyframes drop-197 {
  100% {
    top: 110%;
    left: 100%;
  }
}
@keyframes drop-197 {
  100% {
    top: 110%;
    left: 100%;
  }
}
.confetti-wrapper .confetti-198 {
  width: 6px;
  height: 2.4px;
  background-color: #9c27b0;
  top: -10%;
  left: 85%;
  opacity: 0.6119521067;
  -webkit-transform: rotate(113.211467726deg);
      -ms-transform: rotate(113.211467726deg);
       -o-transform: rotate(113.211467726deg);
          transform: rotate(113.211467726deg);
  -webkit-animation: drop-198 4.2626780342s 0.5071451154s infinite;
       -o-animation: drop-198 4.2626780342s 0.5071451154s infinite;
          animation: drop-198 4.2626780342s 0.5071451154s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-198 {
  100% {
    top: 110%;
    left: 87%;
  }
}
@-o-keyframes drop-198 {
  100% {
    top: 110%;
    left: 87%;
  }
}
@keyframes drop-198 {
  100% {
    top: 110%;
    left: 87%;
  }
}
.confetti-wrapper .confetti-199 {
  width: 1px;
  height: 0.4px;
  background-color: #9c27b0;
  top: -10%;
  left: 37%;
  opacity: 1.2705882907;
  -webkit-transform: rotate(182.248780687deg);
      -ms-transform: rotate(182.248780687deg);
       -o-transform: rotate(182.248780687deg);
          transform: rotate(182.248780687deg);
  -webkit-animation: drop-199 4.2665802244s 0.7782368672s infinite;
       -o-animation: drop-199 4.2665802244s 0.7782368672s infinite;
          animation: drop-199 4.2665802244s 0.7782368672s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-199 {
  100% {
    top: 110%;
    left: 44%;
  }
}
@-o-keyframes drop-199 {
  100% {
    top: 110%;
    left: 44%;
  }
}
@keyframes drop-199 {
  100% {
    top: 110%;
    left: 44%;
  }
}
.confetti-wrapper .confetti-200 {
  width: 8px;
  height: 3.2px;
  background-color: #e91e63;
  top: -10%;
  left: 63%;
  opacity: 0.8706877128;
  -webkit-transform: rotate(5.8377446924deg);
      -ms-transform: rotate(5.8377446924deg);
       -o-transform: rotate(5.8377446924deg);
          transform: rotate(5.8377446924deg);
  -webkit-animation: drop-200 4.418719911s 0.4752575265s infinite;
       -o-animation: drop-200 4.418719911s 0.4752575265s infinite;
          animation: drop-200 4.418719911s 0.4752575265s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-200 {
  100% {
    top: 110%;
    left: 65%;
  }
}
@-o-keyframes drop-200 {
  100% {
    top: 110%;
    left: 65%;
  }
}
@keyframes drop-200 {
  100% {
    top: 110%;
    left: 65%;
  }
}