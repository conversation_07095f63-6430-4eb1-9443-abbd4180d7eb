.wizlet.wizletImagePopUp .card .card-content.card-content {
  background-color: #FFFFFF;
  border-radius: 10px;
}
.wizlet.wizletImagePopUp .card .card-content .card-title {
  font-family: clientBolder, clientBold, Arial;
  font-size: 150%;
  line-height: 100%;
  text-align: left;
  padding-bottom: 5px;
  border-bottom: 3px solid #ed0722;
  margin-bottom: 15px;
}
.wizlet.wizletImagePopUp .card .card-content .card-title.activator, .wizlet.wizletImagePopUp .card .card-content .card-title.reveal {
  position: relative;
}
.wizlet.wizletImagePopUp .card .card-content .card-title.activator i.material-icons.right, .wizlet.wizletImagePopUp .card .card-content .card-title.reveal i.material-icons.right {
  position: absolute;
  top: 0;
  right: -5px;
}
.wizlet.wizletImagePopUp .card .card-image {
  padding: 12px;
}
.wizlet.wizletImagePopUp .card .card-image img.background {
  -o-object-fit: contain;
     object-fit: contain;
}
.wizlet.wizletImagePopUp .card .card-image .image-holder:hover {
  -webkit-filter: saturate(200%);
          filter: saturate(200%);
}
.wizlet.wizletImagePopUp .card .card-image .image-holder:hover img {
  -webkit-transform: scale(1.1);
      -ms-transform: scale(1.1);
       -o-transform: scale(1.1);
          transform: scale(1.1);
}
.wizlet.wizletImagePopUp .card .card-image .image-holder img {
  cursor: pointer;
  max-height: 30vh;
  -o-object-fit: contain;
     object-fit: contain;
  -webkit-filter: drop-shadow(2px 4px 6px black);
          filter: drop-shadow(2px 4px 6px black);
}
.wizlet.wizletImagePopUp .card .card-image .image-holder.background {
  position: absolute;
}
.wizlet.wizletImagePopUp .card .card-image .image-holder.bounce {
  -o-animation-name: shakeInfinite;
  -webkit-animation-name: shakeInfinite;
  animation-name: shakeInfinite;
  -o-animation-duration: 10s;
  -webkit-animation-duration: 10s;
  animation-duration: 10s;
  -o-animation-iteration-count: infinite;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -o-animation-timing-function: ease;
  -webkit-animation-timing-function: ease;
  animation-timing-function: ease;
  -o-animation-fill-mode: forwards;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
}
.wizlet.wizletImagePopUp .card .card-image .image-holder.bounce:nth-child(0) {
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
.wizlet.wizletImagePopUp .card .card-image .image-holder.bounce:nth-child(1) {
  -o-animation-delay: 2s;
  -webkit-animation-delay: 2s;
  animation-delay: 2s;
}
.wizlet.wizletImagePopUp .card .card-image .image-holder.bounce:nth-child(2) {
  -o-animation-delay: 3s;
  -webkit-animation-delay: 3s;
  animation-delay: 3s;
}
.wizlet.wizletImagePopUp .card .card-image .image-holder.bounce:nth-child(3) {
  -o-animation-delay: 4s;
  -webkit-animation-delay: 4s;
  animation-delay: 4s;
}
.wizlet.wizletImagePopUp .card .card-image .image-holder.bounce:nth-child(4) {
  -o-animation-delay: 5s;
  -webkit-animation-delay: 5s;
  animation-delay: 5s;
}
.wizlet.wizletImagePopUp .card .card-image .image-holder.bounce:nth-child(5) {
  -o-animation-delay: 6s;
  -webkit-animation-delay: 6s;
  animation-delay: 6s;
}
.wizlet.wizletImagePopUp .card .card-image .image-holder.bounce:nth-child(6) {
  -o-animation-delay: 7s;
  -webkit-animation-delay: 7s;
  animation-delay: 7s;
}
.wizlet.wizletImagePopUp .card .card-image .image-holder.bounce:nth-child(7) {
  -o-animation-delay: 8s;
  -webkit-animation-delay: 8s;
  animation-delay: 8s;
}
.wizlet.wizletImagePopUp .card .card-image .image-holder.bounce:nth-child(8) {
  -o-animation-delay: 9s;
  -webkit-animation-delay: 9s;
  animation-delay: 9s;
}
.wizlet.wizletImagePopUp .card .card-image .image-holder.bounce:nth-child(9) {
  -o-animation-delay: 10s;
  -webkit-animation-delay: 10s;
  animation-delay: 10s;
}
.wizlet.wizletImagePopUp .container.fullscreen {
  max-height: calc(100vh - 140px);
  width: 100%;
  max-width: 2560px;
  padding: 0;
  margin: auto;
}
.wizlet.wizletImagePopUp .container.fullscreen .card {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  margin: auto;
}
.wizlet.wizletImagePopUp .container.fullscreen .card .card-image {
  max-height: calc(100vh - 140px);
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  margin: auto;
}
.wizlet.wizletImagePopUp .container.fullscreen .card .card-image > img {
  max-height: inherit;
  width: auto;
  margin: auto;
}