define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', '../styles/materialize-src/js/materialize.min'], function ($, Q, WizerApi, WizletBase, doT) {

    var Matchable = function () {
        this.type = 'Matchable';
        this.level = 1;
    };

    Matchable.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;
        this.votesBeforeUpdate = [];

        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];
        
        requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);
        

        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });

    };

    Matchable.prototype.unloadHandler = function () {
        $('#toast-container').hide();
        $('.material-tooltip').remove();
        $(this.wizletContext).unbind("wizer:action:wizletBase:scope-reFetch:complete");
        WizletBase.unloadHandler({ wizlet: this });
    };

    Matchable.prototype.render = function (options) {
        var self = this;
                    
        $(document).one('wizer:action:init', function(e, currentAction) { 
            for (let index = 0; index < 100; index++) {
                self.adjustHeight(true);                
                // self.adjustHeight_Right();                
            }
        });

        return self.templateDefer.promise.then(function (template) {
            var fragment = template(options.wizletInfo);
            options.context.html(fragment);
            
            var matchable = options.context.find("#matchable");

            matchable.sortable({ 
                axis: "y",
                placeholder: "ui-state-highlight",
                create: function(event, ui){
                    self.adjustHeight(true);
                },
                start: function(event, ui){
                    ui.placeholder.height(ui.helper.outerHeight());
                    self.adjustHeight(true);
                },
                change: function(evt, ui){
                    //self.adjustHeight(false);
                },
                stop: function(evt, ui){
                    self.adjustHeight(true);
                }
            });
            matchable.disableSelection();

            //matchable.height( "height", matchable.outerHeight());

            if (options.wizletInfo.withHelp) {
                options.context.find('.tooltipped.timage').tooltip( {
                    enterDelay: 100, html: "timage"
                });
                
                //Give the specific class (to difference from another tooltips) to the material-tooltip created elements
                $('.tooltip-content').filter(function() {return $(this).text() == "timage";}).parent().addClass('timage');

                //Add position to the tooltip box to handle in the CSS
                $.each($('.material-tooltip.timage:not(.positioned)'), function (idx, tooltip) {
                    $(tooltip).addClass( "positioned right");  
                });
            }

            options.context.find('#submitBtn').on('click', function (event) {
                
                var binds=[], orders=[];
                var items = options.context.find('ul#matchable li.right-item');

                if (options.wizletInfo.submitBtn.blockAfter) {
                    $.each (items, function(idx, item) {
                       $(item).addClass('disabled');
                    });
                }


                var newTry= false;
                if (options.wizletInfo.id && options.wizletInfo.submitBtn.giveATry) {
                    if (localStorage.getItem( options.wizletInfo.id ) == 1) {
                        newTry= false;
                    } else {
                        newTry= true;
                        localStorage.setItem(options.wizletInfo.id, 1);       
                    }
                } 
                
                if (options.wizletInfo.submitBtn.hideAfter && !newTry) $(this).hide();


                $.each (items, function(idx, item) {
                    binds.push ($(item).data('bind'));
                    orders.push ($(item).data('order'));
                });
                
                self.addVotes(binds);

                if (options.wizletInfo.submitBtn.toast)
                    M.toast({
                        html: options.wizletInfo.submitBtn.toast,
                        classes: 'rounded'
                    });

                var points=0;
                $.each (options.wizletInfo.rightOrder, function(idx, order) {
                    let isCorrect = order==orders[idx];
                    $(items[idx]).find('i.check-icon').removeAttr('hidden').
                                                        html(isCorrect ? 'check_circle' : 'cancel').
                                                        removeClass('green-text red-text').
                                                        addClass(isCorrect ? 'green-text' : 'red-text');
                                                        
                    if (options.wizletInfo.score && options.wizletInfo.score.points && isCorrect)
                        points += parseInt(options.wizletInfo.score.points);
                });

                
                if (options.wizletInfo.score && options.wizletInfo.score.question) {                    
                    self.addVote(options.wizletInfo.score.question, points);
                }
                
                if (options.wizletInfo.submitBtn.idToShow) {
                    $(this).removeClass('pulse');
                    $('#'+options.wizletInfo.submitBtn.idToShow).removeAttr('hidden')
                                                                .find('a:not(.follower)').removeAttr('disabled');
                }

            });


            return true;
        })
        .fail(this.wizerApi.showError);
    };


    Matchable.prototype.addVote = function (questionName, val) {
        var self = this;

        var questionId = self.wizerApi.getQuestionIdByName(questionName);

        var defer = Q.defer();

        var waiting = self.wizerApi.addVotes({
            votes:[{
                questionId:   questionId, 
                responseText: val
            }]});

        waiting.then(function (result) {            
            defer.resolve(result);
        });                    
        
        return defer.promise;
    };

    Matchable.prototype.adjustHeight = function (isStop) {
        
        var self = this; 
        var itemsLeft = self.wizletContext.find("ul.left-list").find("li.left-item");
        var itemsRight = self.wizletContext.find("ul#matchable").find("li.right-item:not(.ui-sortable-helper)");

        itemsLeft.css("height","auto");
        if (isStop) itemsRight.css("height","auto");

        var leftHeight, rightHeight;
        itemsRight.each(function(idx, itemRight){
            leftHeight = parseInt($(itemsLeft[idx]).outerHeight());
            rightHeight = parseInt($(itemRight).outerHeight());
            if (leftHeight > rightHeight) {
                 $(itemRight).height(leftHeight);
            } else {
                $(itemsLeft[idx]).height(rightHeight);
            }
        });
    };

    Matchable.prototype.adjustHeight_Right = function () {
        
        var self = this; 
        var itemsLeft = self.wizletContext.find("ul.left-list").find("li.left-item");
        var itemsRight = self.wizletContext.find("ul#matchable").find("li.right-item:not(.ui-sortable-helper)");

        itemsLeft.css("height","auto");
        itemsRight.css("height","auto");

        var leftHeight, rightHeight;
        itemsRight.each(function(idx, itemRight){
            leftHeight = parseInt($(itemsLeft[idx]).outerHeight());
            rightHeight = parseInt($(itemRight).outerHeight());
            if (rightHeight > leftHeight) {
                 $(itemsLeft[idx]).height(rightHeight);
            } 
        });
    };


    Matchable.prototype.addVotes = function (questionNames) {
        var self = this; 

        var myVotes = [];
        questionNames.forEach(function(q,pos) { 
            myVotes.push( { 
                questionId: self.wizerApi.getQuestionIdByName(q), 
                responseText: (pos+1) } ); 
        });
        self.wizerApi.addVotes({ votes: myVotes });
    };


    Matchable.getRegistration = function () {
        return new Matchable();
    };

    return Matchable;

});