<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayout_SIM_R1" layout="../../../layout/tabsLayout2Case">
  <Include name="Header_SIM_R1"></Include>
  <Include name="KPIgauges_R1"></Include>
  
  
  <!-- Breadcrumbs progress -->
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/breadcrumbs.dot",
      css: "styles/breadcrumbs.css",
      animate: "fadeInLeft",
      progress: "5",
      steps: [
               "!{SIM_BreadCrumbs_1}",
               "!{SIM_BreadCrumbs_2}",
               "!{SIM_BreadCrumbs_3}",
               "!{SIM_BreadCrumbs_4}",
               "!{SIM_BreadCrumbs_5}",
               "!{SIM_BreadCrumbs_6}",
               "!{SIM_BreadCrumbs_7}",
               "!{SIM_BreadCrumbs_8}" ]
    }]]>
  </Component>


  <Component type="Tabs" customJS="true"><![CDATA[{
      templateInEvent: "html/tabs.dot",
      css: "styles/tabs.css",
      header: "!{} ",
      tabs: [
          "!{SIM_R1_Scenario4_Header}",
          "!{SIM_Scenario_Tab2}"
      ],
      scope: null
  }]]></Component>

  
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "horizontal",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R1_Scenario4_Img}",  alt: "!{SIM_R1_Scenario4_Title}" ,
          position: "right", 
          src_vert: "!{}",
          animate: "fadeInRight animate__delay-1s", _animateLater: "bounce"
        },
        _img: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R1_Scenario4_Img}",  alt: "!{SIM_R1_Scenario4_Title}" ,
          isHiddenWhenSmall: true, 
          src_vert: "!{}"
        },
        position: "up",
        animate: "fadeInUp animate__delay-2s",
        title: "!{SIM_R1_Scenario4_Title}",
        body: "!{SIM_R1_Scenario4_Text}"
      }      
    }]]>
  </Component>
  


 <Component type="RadioButtons" customJS="true">
    <![CDATA[{
      templateInEvent: "html/radioButtons.dot",
      css: "styles/radioButtons.css",
      id: "",
      isHidden: false,
      animate: "fadeInRight",
      headerLabel: "!{}",
      header: "!{}",
      instructions: "!{RadioInstructions}",
      titleLabel: "!{}",
      title: "!{SIM_R1_Scenario4_Question}",
      subtitle: "",
      class: "with-gap",
      groupName: "radioGroup",
      preloadSaved: true,
      bind: "Q_SIM_R1_Scenario4",
      moreInfo: "!{Choice_MoreInfo}",
      isCheck: false,
      
      noBlockAnswer: false,
      _optionsFromVote: "Q_Title_Input",
      _moreInfoFromVote: "Q_Desc_Input",
      _includeMyVote: false,

      isInline: false,
      optionWidth: "s6 big",
      options: [
        {
          value: "1",
          label: "!{Choice_Opt1}",
          _icon: "thumb_down", _iconDesign:"mdi-comment-remove",
          title: "!{SIM_R1_Scenario4_Opt1}",
          _titleClass:"client-colors-text text-red",
          moreInfo: "!{SIM_R1_Scenario4_Des1}",
          isCorrect: false
        },
        {
          value: "2",
          label: "!{Choice_Opt2}",
          title: "!{SIM_R1_Scenario4_Opt2}",
          moreInfo: "!{SIM_R1_Scenario4_Des2}",
          isCorrect: false
        },
        {
          value: "3",
          label: "!{Choice_Opt3}",
          title: "!{SIM_R1_Scenario4_Opt3}",
          moreInfo: "!{SIM_R1_Scenario4_Des3}",
          isCorrect: false
        }
      ],
      
      autocheck: false,
      checkBtn: {
        label: "!{Choice_btnCheck}",
        animate: "slideInUp",
        _toast: { 
          ifCorrect: "!{Choice_toastCorrect}",
          ifIncorrect: "!{Choice_toastIncorrect}",
          points: "!{}"
        },
        toastMsg: "!{InputSubmited}",
        idToClick: "navButton",
        _idToShow: "navButton",
        idToShowIfCorrect: "",
        idToShowIfIncorrect: "",
        scrollToDown: false
      },
      
      _score : {
        points: { ifCorrect: "!{Quiz_pointsIfCorrect}", ifIncorrect: "!{Quiz_pointsIfIncorrect}"},
        question: "Score_SIM_R1_Scenario4",
        _questions: [ ],
        _total: "Score_SIM_R1_Scenario4_Total"
      },
      
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower"]     
    }]]>
  </Component>

  

  


  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton",
      isHidden: true, _showDelay: "5000",
      hasModals: false,
      buttons: [
        {
          type: "next",
          pulse: true,
          _gdActionEmbed: "SIM_R1_Scenario4_AGG",
          _gdActionTrack: "GD",
          _gdActionSection: "",
          _targetSection: "",
          label: "!{Navigation_feedback}",
          tooltip: "!{Navigation_feedback_tt}",
          icon: "forward"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>




</Action>