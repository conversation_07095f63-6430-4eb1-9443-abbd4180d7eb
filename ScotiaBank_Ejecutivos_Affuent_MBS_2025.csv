Customer:;SCOTIABANK;SCOTIABANK;
Event:;ScotiaBank_Ejecutivos_Affuent_MBS_2025;;;;;Event;;ScotiaBank Ejecutivos Affuent;

Questions:;

GD;Individual,Protected;GD;
GD_Foreman;Individual,Protected;GD_Foreman;
GD_Fac;Individual,Protected;GD_Fac;
GD_Fac_Foreman;Individual,Protected;GD_Fac_Foreman;
Follower;Individual,Protected;Follower;
Team;Individual,Protected;Team;
Team_Foreman;Individual,Protected;Team_Foreman;
Filter_Agg;Individual,Protected;Filter_Agg;


Q_LOGIN_DATE_R1;Individual;Q_LOGIN_DATE_R1;
Q_LOGIN_DATE_R2;Individual;Q_LOGIN_DATE_R2;
Q_LOGIN_DATE_R3;Individual;Q_LOGIN_DATE_R3;
Q_FINISH_DATE_R1;Individual;Q_FINISH_DATE_R1;
Q_FINISH_DATE_R2;Individual;Q_FINISH_DATE_R2;
Q_FINISH_DATE_R3;Individual;Q_FINISH_DATE_R3;


Q_My_Name;Individual;Q_My_Name;
Q_My_Avatar;Individual;Q_My_Avatar;

Q_FAC_Navigation_Tab;Individual;Q_FAC_Navigation_Tab;

ConstantZeroPointTen;Individual;ConstantZeroPointTen;
ConstantZeroPointFive;Individual;ConstantZeroPointFive;
ConstantZeroPointTwo;Individual;ConstantZeroPointTwo;
ConstantTwo;Individual;ConstantTwo;
ConstantMinusOne;Individual;ConstantMinusOne;

Score_SIM_Init_KPI1;Individual;Score_SIM_Init_KPI1;
Score_SIM_Init_KPI2;Individual;Score_SIM_Init_KPI2;
Score_SIM_Init_KPI3;Individual;Score_SIM_Init_KPI3;
Score_SIM_Init_KPI4;Individual;Score_SIM_Init_KPI4;


Score_SIM_Total_KPI1;Individual;Score_SIM_Total_KPI1;
Score_SIM_Total_KPI2;Individual;Score_SIM_Total_KPI2;
Score_SIM_Total_KPI3;Individual;Score_SIM_Total_KPI3;
Score_SIM_Total_KPI4;Individual;Score_SIM_Total_KPI4;


Score_SIM_Total_KPI1_R1;Individual;Score_SIM_Total_KPI1_R1;
Score_SIM_Total_KPI2_R1;Individual;Score_SIM_Total_KPI2_R1;
Score_SIM_Total_KPI3_R1;Individual;Score_SIM_Total_KPI3_R1;
Score_SIM_Total_KPI4_R1;Individual;Score_SIM_Total_KPI4_R1;
Score_SIM_Total_KPI4_R1_Adjusted;Individual;Score_SIM_Total_KPI4_R1_Adjusted;
Score_SIM_Total_KPI4_R1_Negative;Individual;Score_SIM_Total_KPI4_R1_Negative;
Score_SIM_Total_KPI3_Adjusted;Individual;Score_SIM_Total_KPI3_Adjusted;

Score_SIM_Total_KPI1_R2;Individual;Score_SIM_Total_KPI1_R2;
Score_SIM_Total_KPI2_R2;Individual;Score_SIM_Total_KPI2_R2;
Score_SIM_Total_KPI3_R2;Individual;Score_SIM_Total_KPI3_R2;
Score_SIM_Total_KPI4_R2;Individual;Score_SIM_Total_KPI4_R2;


Score_SIM_Total_KPI1_R3;Individual;Score_SIM_Total_KPI1_R3;
Score_SIM_Total_KPI2_R3;Individual;Score_SIM_Total_KPI2_R3;
Score_SIM_Total_KPI3_R3;Individual;Score_SIM_Total_KPI3_R3;
Score_SIM_Total_KPI4_R3;Individual;Score_SIM_Total_KPI4_R3;


Score_SIM_Total;Individual;Score_SIM_Total;
Score_SIM_Total_Rank;Individual;Score_SIM_Total_Rank;


Score_SIM_Total_R1;Individual;Score_SIM_Total_R1;
Score_SIM_Total_R1_Adjusted;Individual;Score_SIM_Total_R1_Adjusted;
Score_SIM_Total_R1_Rank;Individual;Score_SIM_Total_R1_Rank;

Score_SIM_Total_TOTAL;Individual;Score_SIM_Total_TOTAL;
Score_SIM_Total_TOTAL_Rank;Individual;Score_SIM_Total_TOTAL_Rank;


Q_SIM_Initiatives;Individual;Q_SIM_Initiatives;
    Q_SIM_Initiatives_1;Individual;Q_SIM_Initiatives_1;0:!{Choice_OptNO};1:!{Choice_OptYES};
    Q_SIM_Initiatives_2;Individual;Q_SIM_Initiatives_2;0:!{Choice_OptNO};1:!{Choice_OptYES};
    Q_SIM_Initiatives_3;Individual;Q_SIM_Initiatives_3;0:!{Choice_OptNO};1:!{Choice_OptYES};
    Q_SIM_Initiatives_4;Individual;Q_SIM_Initiatives_4;0:!{Choice_OptNO};1:!{Choice_OptYES};
    Q_SIM_Initiatives_5;Individual;Q_SIM_Initiatives_5;0:!{Choice_OptNO};1:!{Choice_OptYES};
    Q_SIM_Initiatives_6;Individual;Q_SIM_Initiatives_6;0:!{Choice_OptNO};1:!{Choice_OptYES};
    Q_SIM_Initiatives_7;Individual;Q_SIM_Initiatives_7;0:!{Choice_OptNO};1:!{Choice_OptYES};
    Q_SIM_Initiatives_8;Individual;Q_SIM_Initiatives_8;0:!{Choice_OptNO};1:!{Choice_OptYES};
    Q_SIM_Initiatives_9;Individual;Q_SIM_Initiatives_9;0:!{Choice_OptNO};1:!{Choice_OptYES};
    Q_SIM_Initiatives_10;Individual;Q_SIM_Initiatives_10;0:!{Choice_OptNO};1:!{Choice_OptYES};
    Q_SIM_Initiatives_11;Individual;Q_SIM_Initiatives_11;0:!{Choice_OptNO};1:!{Choice_OptYES};
    Q_SIM_Initiatives_12;Individual;Q_SIM_Initiatives_12;0:!{Choice_OptNO};1:!{Choice_OptYES};
    Q_SIM_Initiatives_13;Individual;Q_SIM_Initiatives_13;0:!{Choice_OptNO};1:!{Choice_OptYES};
    Q_SIM_Initiatives_14;Individual;Q_SIM_Initiatives_14;0:!{Choice_OptNO};1:!{Choice_OptYES};

Q_SIM_R1_Initiatives;Individual;Q_SIM_R1_Initiatives;
    Q_SIM_R1_Initiatives_1;Individual;Q_SIM_R1_Initiatives_1;0:!{Choice_OptNone};1:!{Choice_OptYES};
    Q_SIM_R1_Initiatives_2;Individual;Q_SIM_R1_Initiatives_2;0:!{Choice_OptNone};1:!{Choice_OptYES};
    Q_SIM_R1_Initiatives_3;Individual;Q_SIM_R1_Initiatives_3;0:!{Choice_OptNone};1:!{Choice_OptYES};
    Q_SIM_R1_Initiatives_4;Individual;Q_SIM_R1_Initiatives_4;0:!{Choice_OptNone};1:!{Choice_OptYES};
    Q_SIM_R1_Initiatives_5;Individual;Q_SIM_R1_Initiatives_5;0:!{Choice_OptNone};1:!{Choice_OptYES};
    Q_SIM_R1_Initiatives_6;Individual;Q_SIM_R1_Initiatives_6;0:!{Choice_OptNone};1:!{Choice_OptYES};
    Q_SIM_R1_Initiatives_7;Individual;Q_SIM_R1_Initiatives_7;0:!{Choice_OptNone};1:!{Choice_OptYES};
    Q_SIM_R1_Initiatives_8;Individual;Q_SIM_R1_Initiatives_8;0:!{Choice_OptNone};1:!{Choice_OptYES};
    Q_SIM_R1_Initiatives_9;Individual;Q_SIM_R1_Initiatives_9;0:!{Choice_OptNone};1:!{Choice_OptYES};
    Q_SIM_R1_Initiatives_10;Individual;Q_SIM_R1_Initiatives_10;0:!{Choice_OptNone};1:!{Choice_OptYES};
    Q_SIM_R1_Initiatives_11;Individual;Q_SIM_R1_Initiatives_11;0:!{Choice_OptNone};1:!{Choice_OptYES};
    Q_SIM_R1_Initiatives_12;Individual;Q_SIM_R1_Initiatives_12;0:!{Choice_OptNone};1:!{Choice_OptYES};
    Q_SIM_R1_Initiatives_13;Individual;Q_SIM_R1_Initiatives_13;0:!{Choice_OptNone};1:!{Choice_OptYES};
    Q_SIM_R1_Initiatives_14;Individual;Q_SIM_R1_Initiatives_14;0:!{Choice_OptNone};1:!{Choice_OptYES};

        Score_SIM_R1_Initiatives_KPI1;Individual;Score_SIM_R1_Initiatives_KPI1;
        Score_SIM_R1_Initiatives_KPI2;Individual;Score_SIM_R1_Initiatives_KPI2;
        Score_SIM_R1_Initiatives_KPI3;Individual;Score_SIM_R1_Initiatives_KPI3;
        Score_SIM_R1_Initiatives_KPI4;Individual;Score_SIM_R1_Initiatives_KPI4;

Q_SIM_R1_Scenario1;Individual;Q_SIM_R1_Scenario1;1:!{Choice_Opt1};2:!{Choice_Opt2};3:!{Choice_Opt3};

        Score_SIM_R1_Scenario1_KPI1;Individual;Score_SIM_R1_Scenario1_KPI1;
        Score_SIM_R1_Scenario1_KPI2;Individual;Score_SIM_R1_Scenario1_KPI2;
        Score_SIM_R1_Scenario1_KPI3;Individual;Score_SIM_R1_Scenario1_KPI3;
        Score_SIM_R1_Scenario1_KPI4;Individual;Score_SIM_R1_Scenario1_KPI4;

Q_SIM_R1_Scenario2;Individual;Q_SIM_R1_Scenario2;1:!{Choice_Opt1};2:!{Choice_Opt2};

        Score_SIM_R1_Scenario2_KPI1;Individual;Score_SIM_R1_Scenario2_KPI1;
        Score_SIM_R1_Scenario2_KPI2;Individual;Score_SIM_R1_Scenario2_KPI2;
        Score_SIM_R1_Scenario2_KPI3;Individual;Score_SIM_R1_Scenario2_KPI3;
        Score_SIM_R1_Scenario2_KPI4;Individual;Score_SIM_R1_Scenario2_KPI4;

Q_SIM_R1_Scenario3;Individual;Q_SIM_R1_Scenario3;1:!{Choice_Opt1};2:!{Choice_Opt2};3:!{Choice_Opt3};

        Score_SIM_R1_Scenario3_KPI1;Individual;Score_SIM_R1_Scenario3_KPI1;
        Score_SIM_R1_Scenario3_KPI2;Individual;Score_SIM_R1_Scenario3_KPI2;
        Score_SIM_R1_Scenario3_KPI3;Individual;Score_SIM_R1_Scenario3_KPI3;
        Score_SIM_R1_Scenario3_KPI4;Individual;Score_SIM_R1_Scenario3_KPI4;

Q_SIM_R1_Scenario4;Individual;Q_SIM_R1_Scenario4;1:!{Choice_Opt1};2:!{Choice_Opt2};3:!{Choice_Opt3};

        Score_SIM_R1_Scenario4_KPI1;Individual;Score_SIM_R1_Scenario4_KPI1;
        Score_SIM_R1_Scenario4_KPI2;Individual;Score_SIM_R1_Scenario4_KPI2;
        Score_SIM_R1_Scenario4_KPI3;Individual;Score_SIM_R1_Scenario4_KPI3;
        Score_SIM_R1_Scenario4_KPI4;Individual;Score_SIM_R1_Scenario4_KPI4;

Q_SIM_R1_Scenario5;Individual;Q_SIM_R1_Scenario5;1:!{Choice_Opt1};2:!{Choice_Opt2};3:!{Choice_Opt3};

        Score_SIM_R1_Scenario5_KPI1;Individual;Score_SIM_R1_Scenario5_KPI1;
        Score_SIM_R1_Scenario5_KPI2;Individual;Score_SIM_R1_Scenario5_KPI2;
        Score_SIM_R1_Scenario5_KPI3;Individual;Score_SIM_R1_Scenario5_KPI3;
        Score_SIM_R1_Scenario5_KPI4;Individual;Score_SIM_R1_Scenario5_KPI4;

Q_SIM_R1_Scenario6;Individual;Q_SIM_R1_Scenario6;1:!{Choice_Opt1};2:!{Choice_Opt2};

        Score_SIM_R1_Scenario6_KPI1;Individual;Score_SIM_R1_Scenario6_KPI1;
        Score_SIM_R1_Scenario6_KPI2;Individual;Score_SIM_R1_Scenario6_KPI2;
        Score_SIM_R1_Scenario6_KPI3;Individual;Score_SIM_R1_Scenario6_KPI3;
        Score_SIM_R1_Scenario6_KPI4;Individual;Score_SIM_R1_Scenario6_KPI4;

Q_SIM_R1_Scenario7;Individual;Q_SIM_R1_Scenario7;1:!{Choice_Opt1};2:!{Choice_Opt2};3:!{Choice_Opt3};

        Score_SIM_R1_Scenario7_KPI1;Individual;Score_SIM_R1_Scenario7_KPI1;
        Score_SIM_R1_Scenario7_KPI2;Individual;Score_SIM_R1_Scenario7_KPI2;
        Score_SIM_R1_Scenario7_KPI3;Individual;Score_SIM_R1_Scenario7_KPI3;
        Score_SIM_R1_Scenario7_KPI4;Individual;Score_SIM_R1_Scenario7_KPI4;

Q_Extra_OpenQuestion1_Input;Individual;Q_Extra_OpenQuestion1_Input;
Q_Extra_OpenQuestion2_Input1;Individual;Q_Extra_OpenQuestion2_Input1;
Q_Extra_OpenQuestion2_Input2;Individual;Q_Extra_OpenQuestion2_Input2;
Q_Extra_OpenQuestion3_Input;Individual;Q_Extra_OpenQuestion3_Input;

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;


Sections:;Actions;Template;Related Question;Custom Parameter;

R1_LandingPage;;;
;ShowSlide;Show;;R1_LandingPage;
LandingPage_FAC;;;
;ShowSlide;Show;;LandingPage_FAC;
R1_LandingPage_FAC;;;
;ShowSlide;Show;;R1_LandingPage_FAC;
R1_DebriefPage;;;
;ShowSlide;Show;;R1_DebriefPage;
R1_DebriefPage_FAC;;;
;ShowSlide;Show;;R1_DebriefPage_FAC;
R1_DebriefPage1_FAC;;;
;ShowSlide;Show;;R1_DebriefPage1_FAC;
R1_DebriefPage2_FAC;;;
;ShowSlide;Show;;R1_DebriefPage2_FAC;


LandingPage_Pause;;;
;ShowSlide;Show;;LandingPage_Pause;

TeamName;;;
;ShowSlide;Show;;TeamName;
TeamName_FAC;;;
;ShowSlide;Show;;TeamName_FAC;
TeamName_Table;;;
;ShowSlide;Show;;TeamName_Table;
TeamName_Table_FAC;;;
;ShowSlide;Show;;TeamName_Table_FAC;

SIM_R1_FAC_dashboard;;;
;ShowSlide;Show;;SIM_R1_FAC_dashboard;

SIM_R1_Start;;11;
;ShowSlide;Show;;SIM_R1_Start;
SIM_R1_TeamName;;15;
;ShowSlide;Show;;SIM_R1_TeamName;
SIM_CaseStudy;;20;
;ShowSlide;Show;;SIM_CaseStudy;

SIM_R1_Initiatives;;25;
;ShowSlide;Show;;SIM_R1_Initiatives;
SIM_R1_Initiatives_AGG;;26;
;ShowSlide;Show;;SIM_R1_Initiatives_AGG;
SIM_R1_Initiatives_FB;;27;
;ShowSlide;Show;;SIM_R1_Initiatives_FB;

SIM_R1_Scenario1;;33;
;ShowSlide;Show;;SIM_R1_Scenario1;
SIM_R1_Scenario1_AGG;;34;
;ShowSlide;Show;;SIM_R1_Scenario1_AGG;
SIM_R1_Scenario1_FB;;35;
;ShowSlide;Show;;SIM_R1_Scenario1_FB;

SIM_R1_FinishFirstPart_BackDirector;;40;
;ShowSlide;Show;;SIM_R1_FinishFirstPart_BackDirector;

SIM_R1_Continue;;41;
;ShowSlide;Show;;SIM_R1_Continue;

SIM_R1_FAC_dashboard1;;42;
;ShowSlide;Show;;SIM_R1_FAC_dashboard1;

SIM_R1_Scenario2;;43;
;ShowSlide;Show;;SIM_R1_Scenario2;
SIM_R1_Scenario2_AGG;;44;
;ShowSlide;Show;;SIM_R1_Scenario2_AGG;
SIM_R1_Scenario2_FB;;45;
;ShowSlide;Show;;SIM_R1_Scenario2_FB;

SIM_R1_Scenario3;;50;
;ShowSlide;Show;;SIM_R1_Scenario3;
SIM_R1_Scenario3_AGG;;52;
;ShowSlide;Show;;SIM_R1_Scenario3_AGG;
SIM_R1_Scenario3_FB;;53;
;ShowSlide;Show;;SIM_R1_Scenario3_FB;


SIM_R1_FinishSecondPart_BackDirector;;55;
;ShowSlide;Show;;SIM_R1_FinishSecondPart_BackDirector;

SIM_R1_Continue2;;57;
;ShowSlide;Show;;SIM_R1_Continue2;

SIM_R1_FAC_dashboard2;;58;
;ShowSlide;Show;;SIM_R1_FAC_dashboard2;

SIM_R1_Scenario4;;60;
;ShowSlide;Show;;SIM_R1_Scenario4;
SIM_R1_Scenario4_AGG;;62;
;ShowSlide;Show;;SIM_R1_Scenario4_AGG;
SIM_R1_Scenario4_FB;;63;
;ShowSlide;Show;;SIM_R1_Scenario4_FB;

SIM_R1_Scenario5;;70;
;ShowSlide;Show;;SIM_R1_Scenario5;
SIM_R1_Scenario5_AGG;;72;
;ShowSlide;Show;;SIM_R1_Scenario5_AGG;
SIM_R1_Scenario5_FB;;73;
;ShowSlide;Show;;SIM_R1_Scenario5_FB;

SIM_R1_Scenario6;;80;
;ShowSlide;Show;;SIM_R1_Scenario6;
SIM_R1_Scenario6_AGG;;82;
;ShowSlide;Show;;SIM_R1_Scenario6_AGG;
SIM_R1_Scenario6_FB;;83;
;ShowSlide;Show;;SIM_R1_Scenario6_FB;
SIM_R1_Scenario7;;84;
;ShowSlide;Show;;SIM_R1_Scenario7;
SIM_R1_Scenario7_AGG;;85;
;ShowSlide;Show;;SIM_R1_Scenario7_AGG;
SIM_R1_Scenario7_FB;;86;
;ShowSlide;Show;;SIM_R1_Scenario7_FB;


SIM_R1Other_Finish_BackDirector;;90;
;ShowSlide;Show;;SIM_R1Other_Finish_BackDirector;

Conditional_Screen;
;ShowSlide;Show;;Conditional_Screen;

SIM_R1_Finish_Good;;;
;ShowSlide;Show;;SIM_R1_Finish_Good;

SIM_R1_Finish_Bad;;;
;ShowSlide;Show;;SIM_R1_Finish_Bad;

SIM_R1_FirstPause;;;
;ShowSlide;Show;;SIM_R1_FirstPause;
SIM_R1_SecondPause;;;
;ShowSlide;Show;;SIM_R1_SecondPause;
SIM_R1_Finish_FAC;;;
;ShowSlide;Show;;SIM_R1_Finish_FAC;


Extra_OpenQuestion1_Input;
;ShowSlide;Show;;Extra_OpenQuestion1_Input;
Extra_OpenQuestion1_Answers_FAC;
;ShowSlide;Show;;Extra_OpenQuestion1_Answers_FAC;
Extra_OpenQuestion2_Input;
;ShowSlide;Show;;Extra_OpenQuestion2_Input;
Extra_OpenQuestion2_Answers_FAC;
;ShowSlide;Show;;Extra_OpenQuestion2_Answers_FAC;
Extra_OpenQuestion3_Input;
;ShowSlide;Show;;Extra_OpenQuestion3_Input;
Extra_OpenQuestion3_Answers_FAC;
;ShowSlide;Show;;Extra_OpenQuestion3_Answers_FAC;




;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;


TextContent:;es:Spanish;
Header_logo;/images/logo.png;
Header_logo_menu;/images/logo.png;
;;
Header_LinkSection_Info;Información;
Header_LinkSection_Feedbacks;Feedbacks;
Header_Link_Feedback1;Feedbacks Ronda 1;
Header_Link_Feedback2;Feedbacks Ronda 2;
Header_Link_Feedback3;Feedbacks Ronda 3;
Header_LinkSection_Control;Control;
Header_Link_ControlStart;Reiniciar;
Header_Link_ControlFinish;Finalizar;
Header_LinkLogins;Accesos;
Header_LinkLogins_R1;Accesos Ronda 1;
Header_LinkLogins_R2;Accesos Ronda 2;
Header_LinkLogins_R3;Accesos Ronda 3;
Header_LinkCase;Caso de Estudio;
Header_LinkAssess;Role-Play;
Header_LinkSummary;Resumen;
Header_LinkSummary1;Resumen Ronda 1;
Header_LinkSummary2;Resumen Ronda 2;
Header_LinkSummary3;Resumen Ronda 3;
Header_LinkStrategy;Tu estrategia;
Header_LinkSwot;Análisis PEAT;
Header_LinkTeams;Equipos;
Header_Reset;Borrar mis datos;
Header_LinkRefresh;Actualizar;
Header_LinkHome;Inicio;
Header_LinkExit;Salir;
Header_LinkClose;Cerrar;
Header_Follower;Seguidor;
;;
Header_LinkRanking;Ranking;
Header_LinkLeague;Clasificación;
Header_LinkResults;Resultados;
Header_LinkChoices;Respuestas;
Header_LinkOrder;Orden;
Header_LinkChart;Gráfica;
Header_LinkTable;Tabla;
Header_LinkGlossary;Glosario;
Header_LinkSolution;Solución;
Header_LinkScenario;Escenario;
Header_LinkDecision;Decisión;
;;
Header_LinkSection_Language;Idioma;
Header_LinkLanguageEn;Inglés;
Header_LinkLanguageEs;Español;
;;
Header_LinkHelp;Ayuda;
Header_Modal_Help_Text;"<h3>Contact</h3><img class="embed left small" src="/Wizer/Pages/Events/BBVA_DimLiderazgo_MBS/images/bts.png" style="border-radius:10px">Esta es una simulación desarrollada para el pograma Sales Camp de Holcim. <br>Si tienes preguntas o problemas técnicos, puedes contactarnos en esta dirección de correo: <b><a href="mailto:<EMAIL>&subject=Holcim Sales Camp MBS"><i><EMAIL></i><i class="tiny material-icons">mail_outline</i></a></b>";
;;
Header_Modal_ResetHeader;Borrar mis datos;
Header_Modal_ResetText;"<span class=""strong"">¿Estás seguro de que quieres borrar todas tus respuestas y puntuación almacenada?</span><br>Si lo estás, todo tu información será eliminada para que comiences de nuevo.";
Header_Modal_ResetTitle;Introduce contraseña:;
Header_Modal_Password;Contraseña;
Header_Modal_Code;287;
Header_Modal_PasswordRight;Contraseña correcta;
Header_Modal_PasswordWrong;Contraseña incorrecta;
Header_Modal_ResetBtn;Borrar información;
Header_Modal_ResetToast;Todos los datos borrados;
;;
Header_Modal_Logout_Title;Salir de la aplicación;
Header_Modal_Logout_Text;Por favor, confirma que quieres cerrar sesión.;
Header_Modal_Logout_Close;Cancelar;
Header_Modal_Logout_Logout;Sí, salir;
;;
;;
Header_Modal_Assess_Title;Asignar puntuaciones;
Header_Modal_Assess_Text;A continuación, puedes asignar valores para una puntuación de cada uno de los usuarios:;
Header_Modal_Assess_Tab1;Ronda 1;
Header_Modal_Assess_Tab2;Ronda 2;
Header_Modal_Assess_Tab3;Ronda 3;
Header_Modal_Assess_Head1;Usuario;
Header_Modal_Assess_Head2;Variable;
Header_Modal_Assess_Head3;Valor;
Header_Modal_Assess_Submit;Guardar;
;;
;;
Landing_Title;SCOTIABANK - Ejecutivos Affluent
Landing_Subtitle;Bienvenid@;
Landing_Subtitle_R2;Bienvenid@ - Ronda 2;
Landing_Subtitle_R3;Bienvenid@ - Round 3;
;;
Footer_Title;;
Footer_Subtitle;;
Footer_Copyright;BTS;
Footer_Link;https://www.bts.com;
Footer_Img;;
;;
;;
;;
Navigation_Header;Saltar a pantalla...;
Navigation_menu;Pantallas;
Navigation_menu_tt;ir a pantalla;
Navigation_previous;Anterior;
Navigation_previous_tt;ir a anterior;
Navigation_next;Siguiente;
Navigation_next_tt;ir a siguiente;
Navigation_nextTab;Siguiente Pestaña;
Navigation_nextTab_tt;ir a siguiente pestaña;
Navigation_start;Comenzar;
Navigation_start_tt;comenzar actividad;
Navigation_westart;Comencemos;
Navigation_westart_tt;comenzar;
Navigation_restart;Repetir;
Navigation_restart_tt;reiniciar quiz;
Navigation_feedback;Feedback;
Navigation_feedback_tt;feedback de la decisión;
Navigation_moreInfo;Más detalles;
Navigation_moreInfo_tt;ver más detalles;
Navigation_readEmail;Leer email;
Navigation_readEmail_tt;Leer texto ampliado;
Navigation_summary;Resumen;
Navigation_summary_tt;ver detalles;
Navigation_submit;Confirmar;
Navigation_submit_tt;Confirmar respuestas;
Navigation_submitAll;Confirmar Todo;
Navigation_submit_tt;Confirmar respuestas;
Navigation_submitModalHeader;Confirmar Todo;
Navigation_submitModalText;¿Estás seguro de que quieres confirmar tu decisión? Una vez confirmada, no podrás cambiarla.;
Navigation_submitModalClose;Cancelar;
Navigation_submitAllModalHeader;Confirmar Todo;
Navigation_submitAllModalText;¿Estás seguro de que quieres confirmar todas tus decisiones? Una vez confirmadas, no podrás cambiarlas.;
Navigation_submitAllModalClose;Cancelar;
Navigation_finish;Finalizar;Finish
Navigation_finish_tt;terminar la actividad;
;;
Navigation_SIM_Start_Header;Reiniciar SIM;
Navigation_SIM_Finish_Header;Finalizar SIM;
Navigation_SIM_Start_Text;<span class=""highlight"">Quieres reiniciar la simulación y recuperar el control? (no se perderá ninguna de las respuestas guardadas)</span>;
Navigation_SIM_Finish_Text;<span class=""highlight"">Quieres finalizar la simulación y ceder el control? (no se perderá ninguna de las respuestas guardadas)</span>;
Navigation_SIM_Title;Introducir contraseña:;
Navigation_SIM_Password;Contraseña;
Navigation_SIM_Code;bts;
Navigation_SIM_PasswordRight;Contraseña correcta;
Navigation_SIM_PasswordWrong;Contraseña incorrecta;
Navigation_SIM_Start_Btn;Reiniciar;
Navigation_SIM_Finish_Btn;Finalizar;
Navigation_SIM_Start_Toast;;
Navigation_SIM_Finish_Toast;;
;;
;;
GroupDirector_TitleGD;Director de sesión;
GroupDirector_Results_TitleGD;Consulta de resultados;
GroupDirector_VoteLabel;Decisiones tomadas;
GroupDirector_TitleFAC;Pantalla de resultados;
GroupDirector_ExportBtn;Exportar datos;
GroupDirector_ExportBtn_R1;Exportar datos Ronda 1;
GroupDirector_ExportBtn_R2;Exportar datos Ronda 2;
GroupDirector_ExportBtn_R3;Exportar datos Ronda 3;
GroupDirector_Export_Modal_Title;Exportar datos de la sesión;
GroupDirector_Export_Modal_Text;Por favor, confirma que quieres exportar la información de los participantes de la sesión (decisiones, respuestas, impactos, puntuaciones...) en un archivo CSV que se descargará automáticamente en tu ordenador.;
GroupDirector_Export_Modal_Close;No, cancelar;
GroupDirector_Export_Modal_Action;Sí, descargar;
GroupDirector_ResetBtn;Borrar respuestas;
GroupDirector_ResetModalHeader;Borrar respuestas;
GroupDirector_ResetModalText;"<span class=""strong"">¿Estás seguro de que quieres eliminar todas las respuestas y puntuaciones guardadas de los participantes?</span><br>Si aceptas, toda la información se borrará. Haz esto si deseas comenzar un nuevo curso. No lo hagas si el curso ya ha comenzado.";
GroupDirector_ResetModalQuizText;"<span class=""strong"">¿Estás seguro de que quieres eliminar todas las respuestas y puntuaciones guardadas de los participantes?</span><br>Si aceptas, las respuestas de este Test se borrarán. Haz esto si deseas lanzar de nuevo el Test.";
GroupDirector_ResetModalClose;No, cancelar;
GroupDirector_ResetModalReset;Sí, borrar;
;;
Badge_Opt1;1;
Badge_Opt2;2;
Badge_Opt3;3;
Badge_Opt4;4;
Badge_Opt5;5;
Badge_Opt6;6;
Badge_Opt7;7;
Badge_Opt8;8;
Badge_Opt9;9;
Badge_Opt10;10;
Badge_Opt11;11;
Badge_Opt12;12;
Badge_Opt13;13;
Badge_Opt14;14;
Badge_Opt15;15;
Badge_Opt16;16;
Badge_Opt17;17;
Badge_Opt18;18;
Badge_Opt19;19;
Badge_Opt20;20;
Badge_Opt21;21;
Badge_Opt22;22;
Badge_Opt23;23;
Badge_Opt24;24;
Badge_Opt25;25;
Badge_Opt26;26;
Badge_Opt27;27;
Badge_Opt28;28;
Badge_Opt29;29;
Badge_Opt30;30;
;;
Choice_Submitted;Confirmado;
Choice_OptYES;SÍ;
Choice_OptNO;NO;
Choice_OptNone;-;
;;
Choice_MoreInfo;Ver descripción;
Choice_LessInfo;Descripción;
Choice_Opt0;-;
Choice_Opt1;A;
Choice_Opt2;B;
Choice_Opt3;C;
Choice_Opt4;D;
Choice_Opt5;E;
Choice_Opt6;F;
Choice_Opt7;G;
Choice_Opt8;H;
Choice_Correct;Correcta;
Choice_btnCheck;Confirmar;
Choice_btnReset;Resetear;
Choice_btnResults;Resultados;
Choice_btnFeedback;Feedback;
Choice_toastCorrect;¡Has acertado!;
Choice_toastIncorrect;No es correcto...;
;;
Quiz_pointsIfAllCorrect;3;
Quiz_pointsIfSomeCorrect;1;
Quiz_pointsIfCorrect;1;
Quiz_pointsIfIncorrect;0;
;;
Quiz_btnPlay;Jugar;
Quiz_Dice_Header;;
Quiz_Dice_Instructions;¡Enhorabuena! Ahora lanza el dado. El valor obtenido será tu puntuación para esta pregunta.;
Quiz_Dice_Score_pre;Has obtenido ;
Quiz_Dice_Score_post; puntos;
;;
Quiz_Timer_leadText;Tiempo disponible:;
Quiz_Timer_time;1:00;
Quiz_Timer_timeUpText;¡Se acabó el tiempo!;
Quiz_Timer_gongImg;/media/playGong.gif;
Quiz_Timer_gongAudio;/media/playGong.mp3;
Quiz_Timer_gongText;Ya no hay tiempo para responder más preguntas. Podrás ver tu puntuación en la siguiente pantalla...;
;;
;;
Scenario_ChallengeTab;Escenario;
Scenario_ChoiceTab;Decisión;
Scenario_ChoicesTab;Decisiones;
Scenario_pointsIfCorrect;3;
Scenario_pointsIfIncorrect;0;
Scenario_ActionsTab;Plan de cuenta;
;;
Feedback_Correct;¡Correcto!;
Feedback_Incorrect;No es correcto;
Feedback_Almost;¡Casi!;
Feedback_Opt;Opción;
Feedback_MyOpt;Mi respuesta;
;;
Feedback_LottieOK;/content/lottie_right.json;
Feedback_LottieKO;/content/lottie_wrong.json;
Feedback_GifOK;/content/4964-check-mark-success-animation.gif;
Feedback_GifKO;/content/4970-unapproved-cross.gif;
;;
ZoomInstructions;Pulsa sobre la imagen para ampliar.;
Input_GIF;/content/25241-chat-typing.gif;
Input_Lottie;/content/25241-chat-typing.json;
InputInstructions;Escribe tu respuesta y, cuando hayas terminado, pulsa “Guardar” para enviar.;
InputInstructionsMulti;Escribe tu respuesta y, cuando hayas terminado, pulsa “Guardar” para enviar. Puedes enviar tantas como quieras.;
InputInstructionsAnswers;A continuación, se muestra el listado de todas las respuestas dadas por los participantes:;
InputYourAnswers;Mis respuestas:;
InputAllAnswers;Respuestas de todos:;
InputInstructionsYourAnswers;A continuación, se muestra el listado de tus respuestas:;
InputRemaining;caracteres máximo;
InputRight;Campo correcto;
InputWrong;Error, valida el campo;
InputClear;Limpiar;
InputCleared;Texto borrado;
InputSend;Enviar;
InputForm;Confirmar;
InputForm_PopUp;Actualizar;
InputForm_OK;Registrado correctamente;
InputForm_OK_PopUp;Actualizado correctamente;
InputForm_KO;Error en el registro, valida los campos introducidos;
InputName;Nombre guardado;
InputSent;Respuesta enviada;
InputSubmit;Guardar;
InputSubmited;Respuesta guardada;
InputRemoved;Respuesta borrada;
InputCheck;Comprobar;
InputSaved;Salvado;
Me;Yo;
Results_VotesLabel;Respuestas: ;
Results_TeamsLabel;Participantes: ;
RadioInstructions;Selecciona una de las siguientes respuestas y pulsa “Confirmar” para guardarla:;
RadioInstructionsCascade;Selecciona una de las siguientes respuestas y pulsa “Confirmar” para guardarla y mostrar la siguiente:;
RadioInstructionsAnswers;A continuación, se muestra la distribución de respuestas dadas por los participantes:;
CheckboxesInstructions;Selecciona todas las opciones que consideres y pulsa “Confirmar” para guardar tu respuesta:;
CheckboxesInstructionsAnswers;A continuación, se muestra la distribución de respuestas dadas por los participantes:;
AllInitiativesInstructions;A continuación, se muestra la distribución de todas las iniciativas seleccionadas por los participantes:;
AllStrategiesInstructions;A continuación, se muestra la distribución de todas las estrategias seleccionadas por los participantes:;
Checkboxes_maxOptions;Nº máximo de opciones seleccionado;
Checkboxes_allOptions;Opciones seleccionadas;
SlidersInstructionsAnswers;A continuación, la media de las evaluaciones dadas por los participantes:;
Prioritize_Instructions;Por favor, prioriza las opciones que se encuentran a continuación:;
;;
;;
IndividualResults_User;Equipo;
IndividualResults_Choice;Elección;
IndividualResults_Header;Todas las respuestas;
;;
;;
URL_media_server;https://media.btspulse.com/products/pulse_events/clients/XXX;
;;
Ranking_Header;Clasificación;
Ranking_Position;Posición;
Ranking_User;Equipo;
Ranking_Score;Puntos;
Ranking_Score_Test;Puntos del Test;
Ranking_TotalScore;Puntuación total;
Ranking_Trophy_Lottie;/content/lottie_trophy.json;
;;
KPI_Title;Impacto total;
KPI_LTU;LTU;
KPI_Metrics;Indicadores;
KPI_Title_Client1;Liliana Torres;
KPI_Title_Client2;Carlos Puyol;
KPI_Title_Client3;Mario Araujo;
KPI_Title_Client4;Cliente 4;
KPI_ofAllClients;de todos los clientes;
KPI_Table_Col1;Inicial;
KPI_Table_Col2;Actual;
KPI_Metric1;Crecimiento en ingresos (%);
KPI_Metric2;Principalidad (%);
KPI_Metric3;Pulso (%);
KPI_Metric4;UTs;
KPI_Trivia;Trivia;
KPI_Points;Points;
KPI_TOTAL;TOTAL;
KPI_Metric1_R1;Crecimiento en ingresos (%);
KPI_Metric2_R1;Principalidad (%);
KPI_Metric3_R1;Pulso (%);
KPI_Metric4_R1;UTs;
KPI_Trivia_R1;Trivia;
KPI_TOTAL_R1;TOTAL;
KPI_Metric1_R2;Crecimiento en ingresos (%);
KPI_Metric2_R2;Principalidad (%);
KPI_Metric3_R2;Pulso (%);
KPI_Metric4_R2;UTs;
KPI_Trivia_R2;Trivia;
KPI_TOTAL_R2;TOTAL;
KPI_Metric1_R3;Crecimiento en ingresos (%);
KPI_Metric2_R3;Principalidad (%);
KPI_Metric3_R3;Pulso (%);
KPI_Metric4_R3;UTs;
KPI_Trivia_R3;Trivia;
KPI_TOTAL_R3;TOTAL;
KPI_Metric1_Client;Resultados;
KPI_Metric2_Client;Share of Wallet (%);
KPI_Metric3_Client;Ventas (MM);
;;
KPI_LTU_Init;0;
KPI_Metric1_Init;0;
KPI_Metric2_Init;20;
KPI_Metric3_Init;60;
KPI_Metric4_Init;11;

;;
;;
KPI_LTU_Min;-10;
KPI_Metric1_Min;0;
KPI_Metric2_Min;0;
KPI_Metric3_Min;0;
KPI_Metric4_Min;-10;
KPI_TOTAL_Min;-3;
;;
KPI_LTU_Max;0;
KPI_Metric1_Max;10;
KPI_Metric2_Max;40;
KPI_Metric3_Max;90;
KPI_Metric4_Max;15;
;;
;;
KPI_Metric_Factor;0;
;;
KPI_Total_Max;100;
KPI_Total_Max_R2;150;
KPI_ConstantZeroPointTen;0.1;
KPI_ConstantTwo;2;
KPI_ConstantZeroPointFive;0.5;
KPI_ConstantZeroPointTwo;0.2;
KPI_ConstantMinusOne;-1;
KPI_Total_Max_sep;/;
;;
;;
;;
GroupDirector_Tab1;Ronda 1;
GroupDirector_Text1;6 Escenarios;
GroupDirector_Tab2;Ronda 2;
GroupDirector_Text2;3 Escenarios;
GroupDirector_Tab3;Ronda 3;
GroupDirector_Text3;3 Escenarios;
GroupDirector_Tab9;Extras;
GroupDirector_Text9;Preguntas extra;
;;
;;
;;
GD_LandingPage;Página Portada;
LandingPage_Title;¡Bienvenid@s!;
GD_DebriefPage;Resumen de ronda;
GD_DebriefCharts;Gráficas totales de ronda;
GD_DebriefChartsC1;Gráficas Proyectos Landa;
GD_DebriefChartsC2;Gráficas Construlit;
GD_DebriefChartsC3;Gráficas Ferretería Don Pepe;
GD_DebriefChartsC4;Gráficas Holdensa;
GD_SummaryPage;Resumen de ronda;
DebriefPage_Title;¡Gracias!;
;;
GD_R1_LandingPage;Ronda 1;
R1_LandingPage_Title;Ronda 1;
GD_R2_LandingPage;Ronda 2;
R2_LandingPage_Title;Ronda 2;
GD_R3_LandingPage;Ronda 3;
R3_LandingPage_Title;Ronda 3;
;;
;;
LandingPage_ImgFac;/images/backgrounds/logoSplash.png;
LandingPage_Img0;/images/backgrounds/background_animated_orange.svg;
LandingPage_Img1;/images/backgrounds/Picture1.jpg;
LandingPage_Img2;/images/backgrounds/Picture2.jpg;
LandingPage_Img3;/images/backgrounds/Picture3.jpg;
LandingPage_Img4;/images/backgrounds/Picture4.jpg;
LandingPage_Img5;/images/backgrounds/Picture5.jpg;
LandingPage_Img6;/images/backgrounds/Picture6.jpg;
LandingPage_Img7;/images/backgrounds/Picture7.jpg;
LandingPage_Img8;/images/backgrounds/Picture8.jpg;
LandingPage_Img9;/images/backgrounds/Picture9.jpg;
LandingPage_Img10;/images/backgrounds/Picture10.jpg;
;;
;;
;;
;;
;;
;;
GD_TeamName;Nombres de equipo;
GD_TeamName_Table;Tabla de equipos;
;;
;;
TeamName_Header;Nombre y personaje de tu equipo;
TeamName_Title;Tu nombre y personaje;
TeamName_Body;Antes de comenzar la sesión, elige un nombre y un personaje para tu equipo. Pulsa “Confirmar” para guardarlo.;
TeamName_Placeholder;Nuevo nombre...;
TeamName_length;50;
TeamName_Avatar;Personaje;
TeamName_Avatar_select;Elige un personaje;
;;
defaultAvatar;Avatar0.png;
;;
TeamName_Table_InfoTitle;Equipos de la sesión;
TeamName_Table_Position;Equipo;
TeamName_Table_User;Equipo;
TeamName_Table_Name;Nombre escogido;
TeamName_Table_Records;Total de equipos:;
;;
Logins_Table_InfoTitle;Registro de accesos;
Logins_Table_InfoTitle_R1;Ronda 1 - accesos;
Logins_Table_InfoTitle_R2;Ronda 2 - accesos;
Logins_Table_InfoTitle_R3;Ronda 3 - accesos;
Logins_Table_Position;Equipo;
Logins_Table_User;Equipo;
Logins_Table_Name;Momento;
Logins_Table_Login;Momento comienzo;
Logins_Table_Logout;Momento final;
Logins_Table_Records;Total de usuarios: ;
Logins_Table_Screen;Pantalla de SIM;
;;
;;
;;
;;
SIM_BreadCrumbs_1;Iniciativas;
SIM_BreadCrumbs_2;Reto # 1;
SIM_BreadCrumbs_3;Reto # 2;
SIM_BreadCrumbs_4;Reto # 3;
SIM_BreadCrumbs_5;Reto # 4;
SIM_BreadCrumbs_6;Reto # 5;
SIM_BreadCrumbs_7;Reto # 6;
SIM_BreadCrumbs_8;Reto # 7;
;;
;;
;;
;;
;;
GD_SIM_R1_Start;Comenzar simulación (1 escenarios);
GD_SIM_R1_Continue;Continuar simulación (2 escenarios);
GD_SIM_R1_Continue1;Continuar simulación (3 escenarios);
;;
SIM_R1_FAC_dashboard_Header;Dashboard Ronda;
SIM_R1_FAC_dashboard_teams;Equipos;
SIM_R1_FAC_dashboard_tab1;Iniciativas;
SIM_R1_FAC_dashboard_tab2;Escenarios;
SIM_R1_FAC_dashboard_ranking;Ranking;
;;
SIM_R1_FAC_debrief_Header;Resumen Ronda;
SIM_R1_FAC_debrief_tab1;Iniciativas;
SIM_R1_FAC_debrief_tab2;Reto. 1;
SIM_R1_FAC_debrief_tab3;Reto. 2;
SIM_R1_FAC_debrief_tab4;Reto. 3;
SIM_R1_FAC_debrief_tab5;Reto. 4;
SIM_R1_FAC_debrief_tab6;Reto. 5;
SIM_R1_FAC_debrief_tab7;Reto. 6;
SIM_R1_FAC_debrief_tab8;Reto 7;
SIM_R1_FAC_debrief_ranking;Ranking;
SIM_R1_FAC_debrief_chart1;Rentabilidad;
SIM_R1_FAC_debrief_chart2;Soluciones Integrales;
SIM_R1_FAC_debrief_chart3;Ventas;
SIM_R1_FAC_debrief_chart4;Métrica 4;
SIM_R1_FAC_debrief_chart_Client;Share of Wallet;
;;
;;
;;
;;
;;
;;
;;
SIM_CaseStudy_Header;Caso de Estudio;
SIM_CaseStudy1_Title;¡Bienvenid@ a ScotiaNova!;
SIM_CaseStudy1_Text;"A partir de este momento eres un ejecutivo PyME y Premium del banco ScotiaNova, una entidad financiera con presencia global y un fuerte posicionamiento en Mexicolandia. Recientemente se ha estado observando un incremento en el nivel de la competencia, por lo que el banco enfrenta retos de diferenciar sus productos, atraer nuevos clientes y retener a los actuales.<br><br>Para superar estos desafíos, has sido contratado para formar parte de una de las sucursales más grandes y con mayor flujo de clientes en una zona de alto crecimiento económico en la capital de Mexicolandia. Tras unos meses de entrenamiento intensivo, estás listo para ejecutar la estrategia comercial de ScotiaNova y asesorar a una cartera compleja de clientes. Tus principales responsabilidades incluyen:<br><ul class=""circle-list""><li>Fortalecer tus habilidades de gestión comercial, en ventas presenciales o telefónicas, utilizando el modelo comercial de ScotiaNova</li><li>Dominar los productos clave y su propuesta de valor</li><li>Prospectar y contactar clientes para mantener un tubo de ventas sano, mientras priorizas su potencial a largo plazo para el banco</li><li>Conocer a tus clientes profundamente para alinear tus acciones comerciales y propuestas a sus necesidades específicas</li><li>Contactar periódicamente a tus clientes garantizando el mejor servicio para construir una relación duradera y aumentar índices de principalidad</li><li>Poner en marcha iniciativas personales, que funcionarán como impulsores de tu desempeño</li></ul><br>Para ejecutar exitosamente tus responsabilidades, se te ha recomendado siempre tener en cuenta el impacto de tus decisiones en 4 métricas clave:<br><ol class=""circle-list""><li>Crecimiento en ingresos: representa el cambio en los ingresos que aportan tus clientes desde tu entrada en ScotiaNova.</li><li>Principalidad: representa el porcentaje de tus clientes que tienen 2 o más productos activos en ScotiaNova.</li><li>Pulso: representa la calidad en el servicio y asesoría percibida por tus clientes en interacciones contigo.</li><li>Unidades de tiempo (UT): representan el tiempo que tienes disponible para tomar decisiones. Si llegas a tener números negativos, podrías impactar negativamente el Pulso.</li></ol><br>A pesar de tener una cartera grande y compleja de clientes, tendrás la oportunidad de profundizar la relación con tres de ellos. Recuerda dirigir tus acciones de manera estratégica para asegurar un impacto positivo en las métricas clave de ScotiaNova.";
SIM_CaseStudy1_Instructions;Haz click para continuar leyendo...;
SIM_CaseStudy2_Title; ;
SIM_CaseStudy2_Text; ;
SIM_CaseStudy2_Instructions; ;
SIM_CaseStudy3_Title;;
SIM_CaseStudy3_Text;"<b>¡Conoce a tus tres clientes!</b>";
SIM_CaseStudy_Next;"<br><br>¡Te deseamos mucha suerte!<br>Cuando estés listo, haz click en <b>“Comenzar”</b>.";
;;
SIM_CaseStudy_Img;/images/scenarios/caseStudy.jpg;
SIM_CaseStudy_Img_R2;/images/scenarios/caseStudy_R2.jpg;
;;
;;
;;
;;
SIM_Client1_Img;/images/scenarios/Cliente1.jpg;
SIM_Client1_Name;Liliana Torres (prospecto);
SIM_Client1_Text;<b>Información Personal:</b> Liliana tiene su propio negocio que ha crecido mucho lo cual la hace una persona muy ocupada.<br><br><b>Información Profesional:</b> Solía tener un puesto alto en una multinacional y empezó su negocio de aceites esenciales que ha tenido una expansión tanto en tiendas físicas como en línea.<br><br><b>Información Familiar:</b><br><br><b>Información Financiera:</b><br><br><b>Oportunidades Futuras:</b>;
SIM_Client2_Img;/images/scenarios/Cliente2.jpg;
SIM_Client2_Name;Carlos Puyol (cliente);
SIM_Client2_Text;<b>Información Personal:</b> Carlos tiene mucho tiempo en el banco.<br><br><b>Información Profesional:</b> Es un Director senior de una empresa de energía.<br><br><b>Información Familiar:</b> Tiene un hijo que está a punto de empezar la universidad y 2 hijos pequeños.<br><br><b>Información Financiera:</b> Tiene una tarjeta de crédito.<br><br><b>Oportunidades Futuras:</b>;
SIM_Client3_Img;/images/scenarios/Cliente3.jpg;
SIM_Client3_Name;Mario Araujo (cliente);
SIM_Client3_Text;<b>Información Personal:</b> Mario lleva mucho tiempo en el banco, llega constantemente a la sucursal a hacer sus operaciones.<br><br><b>Información Profesional:</b> Se retiró el año pasado, tenía una empresa mediana de corredora de seguros y se la dejó a su hijo.<br><br><b>Información Familiar:</b> Vive con su esposa y sus hijos son mayores.<br><br><b>Información Financiera:</b> Tiene en el banco su cuenta de ahorros, la tarjeta de crédito y unas inversiones en notas de crédito.<br><br><b>Oportunidades Futuras:</b> Busca cuidar su patrimonio y tener el control de su dinero.;
SIM_Client4_Img;/images/scenarios/Cliente4.jpg;
SIM_Client4_Name;Aceites Aromágicos;
SIM_Client4_Text;<b>Subsegmento:</b> Pequeña (P) empresa.<br><br><b>Industria:</b> Cosméticos y cuidado personal.<br><br><b>Descripción:</b> Aceites Aromágicos, fundada en 2023, se especializa en la creación y comercialización de aceites esenciales 100% puros y orgánicos. Los productos son extraídos de plantas cultivadas de manera sostenible y están diseñados para promover el bienestar físico y emocional.<br><br><b>Prioridades Estratégicas:</b> Expansión en el mercado ampliando presencia internacional y en línea.<br><br><b>Resultados:</b> Ha duplicado sus ventas desde su fundación en 2023 y la satisfacción de sus clientes es mayor al 95%, por lo que la recurrencia de compra también ha crecido considerablemente.<br><br><b>Oportunidades:</b><br><br><b>Problemas:</b><br><br><b>Contactos:</b> Liliana Torres (CEO y Fundadora).<br><br><b>Funcionamiento organizacional:</b> El cliente actualmente opera con BlueBank. No obstante, se encuentra valorando opciones para diferentes productos ahora que la empresa está creciendo y formalizándose.<br><br><b>Productos actuales:</b> Ninguno.;
;;
;;
;;
SIM_R1_Initiatives_Header;Iniciativas Estratégicas;
SIM_R1_Initiatives_Title;Iniciativas Estratégicas;
SIM_R1_Initiatives_Text;;
SIM_R1_Initiatives_Img;;
;;
SIM_R1_Initiatives_Instructions;Elige tres de las siguientes iniciativas haciendo click en el botón de confirmar:;
;;
SIM_R1_Initiatives_Question;;
SIM_R1_Initiatives_Opt1;Manejo de CRM;
SIM_R1_Initiatives_Des1;Inviertes tiempo en ver videos tutoriales y preguntar a tus compañeros sobre cómo utilizar el CRM de forma más eficiente y estratégica, para conseguir un uso disciplinado de la actualización e ingreso de datos en el sistema. <em>UTs: 1.</em>;
    SIM_R1_Initiatives_Opt1_FB;;

    SIM_R1_Initiatives_Opt1_KPI1;0;
    SIM_R1_Initiatives_Opt1_KPI2;1;
    SIM_R1_Initiatives_Opt1_KPI3;1;
    SIM_R1_Initiatives_Opt1_KPI4;-1;

SIM_R1_Initiatives_Opt2;Estudio de productos de otras áreas del banco para detectar necesidades;
SIM_R1_Initiatives_Des2;Trabajas proactivamente en conocer los productos bancarios fuera de las áreas Premium y PyME, aprovechando sesiones internas con especialistas y con compañeros del banco, para poder aprender e incorporar ese conocimiento en interacciones con clientes. <em>UTs: 1.</em>;
    SIM_R1_Initiatives_Opt2_FB;;

    SIM_R1_Initiatives_Opt2_KPI1;1;
    SIM_R1_Initiatives_Opt2_KPI2;1;
    SIM_R1_Initiatives_Opt2_KPI3;0;
    SIM_R1_Initiatives_Opt2_KPI4;-1;

SIM_R1_Initiatives_Opt3;Esfuerzo de prospección;
SIM_R1_Initiatives_Des3;Dedicas un tiempo para analizar las campañas existentes en el banco y buscar prospectos de calidad, cuyas necesidades se alineen con la propuesta de valor de las campañas identificadas. <em>UTs: 1.</em>;
    SIM_R1_Initiatives_Opt3_FB;;

    SIM_R1_Initiatives_Opt3_KPI1;3;
    SIM_R1_Initiatives_Opt3_KPI2;0;
    SIM_R1_Initiatives_Opt3_KPI3;0;
    SIM_R1_Initiatives_Opt3_KPI4;-1;

SIM_R1_Initiatives_Opt4;Entendimiento de los perfiles de clientes;
SIM_R1_Initiatives_Des4;Estudias los distintos perfiles de tus clientes y qué productos son los mejores para satisfacer distintas necesidades, con el objetivo de tener conversaciones de mayor valor. <em>UTs: 1.</em>;
    SIM_R1_Initiatives_Opt4_FB;;

    SIM_R1_Initiatives_Opt4_KPI1;1;
    SIM_R1_Initiatives_Opt4_KPI2;0;
    SIM_R1_Initiatives_Opt4_KPI3;2;
    SIM_R1_Initiatives_Opt4_KPI4;-1;

SIM_R1_Initiatives_Opt5;Curso de "Asesoría Financiera";
SIM_R1_Initiatives_Des5;Te inscribes a un curso de ScotiaNova para mejorar tus habilidades de asesoría y técnicas de comunicación comercial. Este curso te permitirá encontrar mejores prácticas para atraer y retener clientes, pero sobre todo para generar relaciones duraderas. <em>UTs: 1.</em>;
    SIM_R1_Initiatives_Opt5_FB;;

    SIM_R1_Initiatives_Opt5_KPI1;0;
    SIM_R1_Initiatives_Opt5_KPI2;0;
    SIM_R1_Initiatives_Opt5_KPI3;3;
    SIM_R1_Initiatives_Opt5_KPI4;-1;

SIM_R1_Initiatives_Opt6;Conociendo a la competencia;
SIM_R1_Initiatives_Des6;Inviertes tiempo en entender las tasas, comisiones y términos que ofrece la competencia en los productos Premium y PyME más importantes. Analizas cómo se comparan con los de ScotiaNova, para mejorar tu habilidad de comunicar la oferta de valor. <em>UTs: 1.</em>;
    SIM_R1_Initiatives_Opt6_FB;;

    SIM_R1_Initiatives_Opt6_KPI1;0;
    SIM_R1_Initiatives_Opt6_KPI2;1;
    SIM_R1_Initiatives_Opt6_KPI3;1;
    SIM_R1_Initiatives_Opt6_KPI4;-1;

SIM_R1_Initiatives_Opt7;Práctica de una interacción telefónica;
SIM_R1_Initiatives_Des7;Siguiendo los guiones de ScotiaNova, ensayas conversaciones telefónicas que tendrías con prospectos y clientes, con foco en no utilizar tecnicismos y enfocándote en transmitir los mensajes clave de forma clara y concisa. <em>UTs: 1.</em>;
    SIM_R1_Initiatives_Opt7_FB;;

    SIM_R1_Initiatives_Opt7_KPI1;1;
    SIM_R1_Initiatives_Opt7_KPI2;0;
    SIM_R1_Initiatives_Opt7_KPI3;1;
    SIM_R1_Initiatives_Opt7_KPI4;-1;

SIM_R1_Initiatives_Opt8;Seguimiento postventa;
SIM_R1_Initiatives_Des8;Diseñas una estrategia para ampliar tu red de contactos mediante el seguimiento postventa, enfocada en generar referidos a través de la excelente calidad de tu servicio incluso después de la venta. <em>UTs: 1.</em>;
    SIM_R1_Initiatives_Opt8_FB;;

    SIM_R1_Initiatives_Opt8_KPI1;2;
    SIM_R1_Initiatives_Opt8_KPI2;0;
    SIM_R1_Initiatives_Opt8_KPI3;1;
    SIM_R1_Initiatives_Opt8_KPI4;-1;

SIM_R1_Initiatives_Opt9;Análisis de tu cartera de clientes;
SIM_R1_Initiatives_Des9;Siguiendo la estrategia de ScotiaNova, analizas tu cartera de clientes con foco en tener más clientes "principales", que utilicen varios productos del banco. Te apoyas del entendimiento de su perfil, necesidades y de las campañas activas en el banco para diseñar estrategias de venta cruzada. <em>UTs: 1.</em>;
    SIM_R1_Initiatives_Opt9_FB;;

    SIM_R1_Initiatives_Opt9_KPI1;0;
    SIM_R1_Initiatives_Opt9_KPI2;3;
    SIM_R1_Initiatives_Opt9_KPI3;0;
    SIM_R1_Initiatives_Opt9_KPI4;-1;

SIM_R1_Initiatives_Opt10; ;
SIM_R1_Initiatives_Des10; ;
    SIM_R1_Initiatives_Opt10_FB;;

    SIM_R1_Initiatives_Opt10_KPI1;0;
    SIM_R1_Initiatives_Opt10_KPI2;0;
    SIM_R1_Initiatives_Opt10_KPI3;0;
    SIM_R1_Initiatives_Opt10_KPI4;0;

SIM_R1_Initiatives_Opt11; ;
SIM_R1_Initiatives_Des11; ;
    SIM_R1_Initiatives_Opt11_FB;;

    SIM_R1_Initiatives_Opt11_KPI1;0;
    SIM_R1_Initiatives_Opt11_KPI2;0;
    SIM_R1_Initiatives_Opt11_KPI3;0;
    SIM_R1_Initiatives_Opt11_KPI4;0;

SIM_R1_Initiatives_Opt12; ;
SIM_R1_Initiatives_Des12; ;
    SIM_R1_Initiatives_Opt12_FB;;

    SIM_R1_Initiatives_Opt12_KPI1;0;
    SIM_R1_Initiatives_Opt12_KPI2;0;
    SIM_R1_Initiatives_Opt12_KPI3;0;
    SIM_R1_Initiatives_Opt12_KPI4;0;

SIM_R1_Initiatives_Opt13; ;
SIM_R1_Initiatives_Des13; ;
    SIM_R1_Initiatives_Opt13_FB;;

    SIM_R1_Initiatives_Opt13_KPI1;0;
    SIM_R1_Initiatives_Opt13_KPI2;0;
    SIM_R1_Initiatives_Opt13_KPI3;0;
    SIM_R1_Initiatives_Opt13_KPI4;0;

SIM_R1_Initiatives_Opt14; ;
SIM_R1_Initiatives_Des14; ;
    SIM_R1_Initiatives_Opt14_FB;;

    SIM_R1_Initiatives_Opt14_KPI1;0;
    SIM_R1_Initiatives_Opt14_KPI2;0;
    SIM_R1_Initiatives_Opt14_KPI3;0;
    SIM_R1_Initiatives_Opt14_KPI4;0;
;;
SIM_R1_Initiatives_FB_Header;Resultados de las iniciativas que elegiste;
SIM_R1_Initiatives_FB_Instructions;Estas son las iniciativas que elegiste para la primera ronda y su impacto en tus métricas:;
SIM_R1_Initiatives_FB_Text;<b>¡Bien hecho!</b><br><br> Las iniciativas que has elegido tienen un impacto positivo en tu organización:;
;;
;;
;;
;;
;;
SIM_Scenario_Tab1;Escenario;
SIM_Scenario_Tab2;Decisión;
;;
SIM_R1_Scenario1_Header;Reto Comercial 1;
SIM_R1_Scenario1_Title;Prospección de nuevos clientes;
SIM_R1_Scenario1_Text;"Es lunes por la mañana y recibes un correo electrónico urgente del equipo de liderazgo de ScotiaNova. El mensaje es claro: hay que enfocarse en prospectar nuevos clientes. "¡Es hora de brillar y cuento contigo!", se lee al finalizar el correo.<br><br>Contactas y agendas una reunión con un cliente referido que sabes de buena fuente que pertenece al segmento Affluent. Sabes que ha migrado de otro banco y parece estar buscando una relación más personalizada.<br><br><b>¿Cómo abordas esta primera conversación para generar conexión, empatía y abrir la puerta a una relación de valor?</b>";
SIM_R1_Scenario1_Img;/images/scenarios/R1scenario1.jpg;
;;
SIM_R1_Scenario1_Instructions;;
;;
SIM_R1_Scenario1_Question;¿Cómo abordas esta primera conversación para generar conexión, empatía y abrir la puerta a una relación de valor?;
SIM_R1_Scenario1_Opt1;Exploras;
SIM_R1_Scenario1_Des1;Inicias la reunión dedicando tiempo a construir la relación y conoces más sobre su historia financiera. Usas preguntas, demuestras conocimiento del entorno económico actual, y haces referencias a situaciones del mercado relevantes para él. Por ahora solo exploras sus necesidades y expectativas. <em>UTs: -3.</em>;
;;
SIM_R1_Scenario1_Opt2;Abordaje mixto;
SIM_R1_Scenario1_Des2;Empiezas preguntando información general de su relación anterior con el banco, muestras empatía y detectas necesidades, combinas diagnóstico con venta temprana enfocada en su punto de dolor, despiertas el interés del cliente con una buena tasa diferenciada que podrías ofrecer. <em>UTs: -2.</em>;
;;
SIM_R1_Scenario1_Opt3;Foco en la necesidad del cliente;
SIM_R1_Scenario1_Des3;Inicias la conversación de forma cordial, haces algunas preguntas para conocer al cliente y rápidamente detectas que está preocupado por cómo proteger su patrimonio en el contexto económico actual. Ante eso, aprovechas la oportunidad para asesorarle con un fondo de inversión de bajo riesgo, explicas sus beneficios con simulaciones de retorno y plazos. Así enfocas tu tiempo en el mayor potencial de negocio. <em>UTs: -1.</em>;
;;
SIM_R1_Scenario1_Opt4;;
SIM_R1_Scenario1_Des4;;
;;
;;
;;
SIM_R1_Scenario1_FB_Header;Reto Comercial 1: Consecuencias;
;;
SIM_R1_Scenario1_FB_Solution;C;
;;
SIM_R1_Scenario1_FB_Opt1_Title;;
SIM_R1_Scenario1_FB_Opt1_Text;¡Excelente elección! Esta opción maximiza la construcción de confianza, genera apertura a relaciones de largo plazo y te posiciona como asesor experto. Invertir más tiempo en diagnóstico al inicio fortalece la fidelización futura y permite una asesoría segmentada con mayor impacto.;
;;
    SIM_R1_Scenario1_Opt1_KPI1;2;
    SIM_R1_Scenario1_Opt1_KPI2;1;
    SIM_R1_Scenario1_Opt1_KPI3;1;
    SIM_R1_Scenario1_Opt1_KPI4;-3;
;;
;;
SIM_R1_Scenario1_FB_Opt2_Title;;
SIM_R1_Scenario1_FB_Opt2_Text;Esta estrategia puede ser útil si el cliente ya viene con una intención clara. Sin embargo, corres el riesgo de sonar genérico o transaccional. Hablar de productos antes de entender a fondo sus necesidades puede limitar la relación a una operación puntual. Considera usar el 100% de esta primera conversación para indagar y construir.;
;;
    SIM_R1_Scenario1_Opt2_KPI1;1;
    SIM_R1_Scenario1_Opt2_KPI2;0;
    SIM_R1_Scenario1_Opt2_KPI3;0;
    SIM_R1_Scenario1_Opt2_KPI4;-2;
;;
SIM_R1_Scenario1_FB_Opt3_Title;;
SIM_R1_Scenario1_FB_Opt3_Text;Tu intención fue genuina y la conversación empezó bien, pero apresuraste la recomendación sin comprender completamente el contexto y los objetivos del cliente. Aunque pareció una oportunidad natural para ofrecer un producto, faltó profundizar en sus preocupaciones reales, estilo de vida, experiencias previas y horizonte financiero. Saltarte estos pasos puede hacer que el cliente sienta que fue llevado a una venta más que a una conversación de valor.;
;;
    SIM_R1_Scenario1_Opt3_KPI1;0;
    SIM_R1_Scenario1_Opt3_KPI2;0;
    SIM_R1_Scenario1_Opt3_KPI3;0;
    SIM_R1_Scenario1_Opt3_KPI4;-1;
;;
;;
;;
SIM_R1_Scenario2_Header;Reto Comercial 2;
SIM_R1_Scenario2_Title;Llamada telefónica;
SIM_R1_Scenario2_Text;"Recibes una base de clientes con productos básicos activos (cuenta y tarjeta), sin asesoría previa en inversiones ni protección.<br><br>Vas a contactar a Isabel, clienta con:<br><br>• Ingresos altos.<br>• Historial de transacciones estables.<br>• Sin actividad en productos de valor.<br><br>Tienes pocos minutos para:<br><br>• Generar confianza y conexión inmediata.<br>• Mostrar conocimiento de su perfil, no solo productos.<br>• Despertar interés por una conversación de asesoría.<br><br><b>¿Cómo abordas tu llamada con Isabel?</b>";
SIM_R1_Scenario2_Img;/images/scenarios/R1scenario2.jpg;
;;
SIM_R1_Scenario2_Instructions;;
;;
SIM_R1_Scenario2_Question;¿Cómo abordas tu llamada con Isabel?;
SIM_R1_Scenario2_Opt1;Crear confianza;
SIM_R1_Scenario2_Des1;"Hola Isabel, soy [nombre] de Scotiabank. Me pongo a tus órdenes como tu nuevo ejecutivo Premium, quería tomarme un momento para saludarte y conocerte mejor. Siempre me interesa entender la historia de nuestros clientes más allá de los números. ¿Te parecería bien si coordinamos una reunión para conversar con calma y conocernos un poco más?" <em>UTs: -3.</em>;
;;
SIM_R1_Scenario2_Opt2;Despertar interés;
SIM_R1_Scenario2_Des2;"Hola Isabel, soy [nombre] de Scotiabank. Estuve revisando tu perfil financiero y, más allá de los productos activos, noté que podrías estar desaprovechando oportunidades para generar ingresos adicionales sin asumir más riesgo. Me encantaría tener una conversación breve contigo para explorar si tiene sentido para ti. ¿Qué opinas?". <em>UTs: -1.</em>;
;;
SIM_R1_Scenario2_Opt3;Técnica de enganche;
SIM_R1_Scenario2_Des3;"Hola Isabel, soy [nombre] de Scotiabank. Estuve revisando tu perfil y noté que, aunque ya manejas productos básicos con nosotros, por tu nivel de ingresos podrías acceder a soluciones más personalizadas para proteger y hacer crecer tu patrimonio. He trabajado con otros clientes similares que, con pequeños ajustes, han logrado mejorar su rentabilidad sin aumentar riesgo. ¿Qué día de la semana te funciona para explorar si esto también aplica a ti?" <em>UTs: -2.</em>;
;;
SIM_R1_Scenario2_Opt4;;
SIM_R1_Scenario2_Des4;;
;;
;;
;;
SIM_R1_Scenario2_FB_Header;Reto Comercial 2: Consecuencias;
;;
SIM_R1_Scenario2_FB_Solution;C;
;;
SIM_R1_Scenario2_FB_Opt1_Title;;
SIM_R1_Scenario2_FB_Opt1_Text;Aunque es una buena táctica para romper el hielo o generar rapport, no comunica por qué la clienta debería dedicar su tiempo. Suena a cita "sin agenda". Si bien puede crear simpatía, no despierta suficiente interés ni urgencia.;
;;
    SIM_R1_Scenario2_Opt1_KPI1;1;
    SIM_R1_Scenario2_Opt1_KPI2;0;
    SIM_R1_Scenario2_Opt1_KPI3;0;
    SIM_R1_Scenario2_Opt1_KPI4;-3;
;;
SIM_R1_Scenario2_FB_Opt2_Title;;
SIM_R1_Scenario2_FB_Opt2_Text;Buena opción, sobre todo si Isabel es una persona que responde a propuestas intrigantes. Sin embargo, podría sentirse genérica sin una referencia clara o ejemplo concreto. No asegura cita si la clienta es escéptica.;
;;
    SIM_R1_Scenario2_Opt2_KPI1;1;
    SIM_R1_Scenario2_Opt2_KPI2;1;
    SIM_R1_Scenario2_Opt2_KPI3;0;
    SIM_R1_Scenario2_Opt2_KPI4;-1;
;;
SIM_R1_Scenario2_FB_Opt3_Title;;
SIM_R1_Scenario2_FB_Opt3_Text;Esta es la mejor opción. Transmite preparación, personalización y propone una conversación con propósito. Demuestra conocimiento experto sin presión y busca cerrar una reunión presencial. Isabel se sentirá valorada, no vendida.;
;;
    SIM_R1_Scenario2_Opt3_KPI1;2;
    SIM_R1_Scenario2_Opt3_KPI2;1;
    SIM_R1_Scenario2_Opt3_KPI3;1;
    SIM_R1_Scenario2_Opt3_KPI4;-2;
;;
SIM_R1_Scenario2_FB_Opt4_Title;;
SIM_R1_Scenario2_FB_Opt4_Text;;
    SIM_R1_Scenario2_Opt4_KPI1;0;
    SIM_R1_Scenario2_Opt4_KPI2;0;
    SIM_R1_Scenario2_Opt4_KPI3;0;
    SIM_R1_Scenario2_Opt4_KPI4;0;
;;
;;
;;
;;
;;
SIM_R1_Scenario3_Header;Reto Comercial 3;
SIM_R1_Scenario3_Title;Conversación con Don Mario;
SIM_R1_Scenario3_Text;"Has contactado a Mario y lograste generar empatía y romper el hielo. Ahora viene tu oportunidad de demostrar que eres un ejecutivo Affluent.<br><br>Mario es cliente que tiene 60 años, empresario destacado, es viudo y tiene 4 hijos adultos. Tres de sus cuatro hijos trabajan en la empresa familiar, y dos siguen viviendo con él.<br><br>En tus comunicaciones con él lo encuentras amigable, va siempre al grano, le disgustan las reuniones muy largas y que le den muchas vueltas al mismo tema.<br><br><b>Tu objetivo es descubrir con certeza lo que es importante para Mario.</b>";
SIM_R1_Scenario3_Img;/images/scenarios/R1scenario3.jpg;
;;
SIM_R1_Scenario3_Instructions;;
;;
SIM_R1_Scenario3_Question;Tu objetivo es descubrir con certeza lo que es importante para Mario.;
SIM_R1_Scenario3_Opt1;Enfoque en lo que sabemos;
SIM_R1_Scenario3_Des1;"Don Mario, tiene bastante tiempo con su cuenta premium en Scotiabank y me ha platicado que su mayor interés es cuidar su patrimonio. ¿Le interesaría conocer opciones de inversión o ampliar sus líneas de crédito?" <em>UTs: -2.</em>;
;;
SIM_R1_Scenario3_Opt2;Enfoque aspiracional;
SIM_R1_Scenario3_Des2;"Don Mario, me gustaría conocer un poco más sobre lo que visualiza para su futuro y el de su familia. ¿Cuáles son sus prioridades hoy en día, tanto personales como familiares?¿Qué le gustaría asegurar o dejar establecido para los suyos en los próximos años?" <em>UTs: 0.</em>;
;;
SIM_R1_Scenario3_Opt3;Enfoque en soluciones;
SIM_R1_Scenario3_Des3;"Don Mario, ¿hay algún reto financiero actual que esté enfrentando? Por ejemplo, temas fiscales, estructura patrimonial o manejo del negocio familiar." <em>UTs: -1.</em>;
;;
SIM_R1_Scenario3_Opt4;;
SIM_R1_Scenario3_Des4;;
;;
;;
;;
SIM_R1_Scenario3_FB_Header;Reto Comercial 3: Consecuencias;
;;
SIM_R1_Scenario3_FB_Solution;B;
;;
SIM_R1_Scenario3_FB_Opt1_Title;;
SIM_R1_Scenario3_FB_Opt1_Text;Demasiado pronto. Aunque parezca personalizado, esta aproximación se centra en productos y no en la persona. Puede generar rechazo o cerrar la conversación. Pierdes la oportunidad de conectar con lo que realmente le importa.;
;;
    SIM_R1_Scenario3_Opt1_KPI1;0;
    SIM_R1_Scenario3_Opt1_KPI2;1;
    SIM_R1_Scenario3_Opt1_KPI3;0;
    SIM_R1_Scenario3_Opt1_KPI4;-2;
;;
SIM_R1_Scenario3_FB_Opt2_Title;;
SIM_R1_Scenario3_FB_Opt2_Text;Excelente. Este tipo de preguntas te posiciona como asesor, no como vendedor. Exploras aspiraciones, legado, y relaciones familiares — pilares clave del segmento Affluent. Evitas asumir, y eso te abre más posibilidades de valor futuro.;
;;
    SIM_R1_Scenario3_Opt2_KPI1;1;
    SIM_R1_Scenario3_Opt2_KPI2;1;
    SIM_R1_Scenario3_Opt2_KPI3;1;
    SIM_R1_Scenario3_Opt2_KPI4;0;
;;
SIM_R1_Scenario3_FB_Opt3_Title;;
SIM_R1_Scenario3_FB_Opt3_Text;Buena intención, pero un poco prematura. Asumes que hay un problema a resolver sin antes entender qué es realmente importante para él. Puede cerrarse si siente que estás siendo muy funcional. Aun así, es mejor que saltar directo al producto.;
;;
    SIM_R1_Scenario3_Opt3_KPI1;0;
    SIM_R1_Scenario3_Opt3_KPI2;0;
    SIM_R1_Scenario3_Opt3_KPI3;0;
    SIM_R1_Scenario3_Opt3_KPI4;-1;
;;
;;
;;
;;
;;
SIM_R1_Scenario4_Header;Reto Comercial 4;
SIM_R1_Scenario4_Title;Oportunidad de vincular a Carlos Puyol;
SIM_R1_Scenario4_Text;"Estás en conversación con Carlos Puyol, cliente Affluent, interesado en asegurar el futuro de su familia.<br><br>Ya discutieron largo tiempo sobre la importancia de invertir. Carlos mencionó que ha considerado fondos de inversión, y le atraen los plazos fijos por sus tasas, ya que planea adquirir una segunda vivienda para generar ingresos por renta.<br><br><b>Ahora es el momento de actuar como un asesor integral, conectando inversión, propósito y aspiraciones.</b>";
SIM_R1_Scenario4_Img;/images/scenarios/R1scenario4.jpg;
;;
SIM_R1_Scenario4_Instructions;;
;;
SIM_R1_Scenario4_Question;Ahora es el momento de actuar como un asesor integral, conectando inversión, propósito y aspiraciones.;
SIM_R1_Scenario4_Opt1;Cambias de foco;
SIM_R1_Scenario4_Des1;"Carlos, si tu objetivo es generar ingresos pasivos con una segunda propiedad, podrías considerar solicitar un crédito hipotecario. Con lo que me has mencionado de tu bono empresarial anual, podrías utilizarlo como parte del enganche y empezar a capitalizar esa inversión desde este año." <em>UTs: -2.</em>;
;;
SIM_R1_Scenario4_Opt2;Paso a paso para asesorarle mejor;
SIM_R1_Scenario4_Des2;"Carlos, considerando tu interés en hacer crecer tu capital desde ya, podríamos empezar con un fondo de inversión de riesgo moderado. Te generará mejores rendimientos que un plazo fijo y te permitirá mover ese capital en cuanto definan el proyecto de la vivienda, así pones a trabajar tu dinero lo antes posible" <em>UTs: -1.</em>;
;;
SIM_R1_Scenario4_Opt3;Haces preguntas;
SIM_R1_Scenario4_Des3;"Carlos, antes de avanzar con una solución específica, me gustaría profundizar un poco más en tus metas y entender cómo visualizas este proyecto familiar a futuro. ¿Qué estrategia financiera has pensado con la familia?; ¿Qué retos se interponen en el camino?" <em>UTs: -3.</em>;
;;
SIM_R1_Scenario4_Opt4;;
SIM_R1_Scenario4_Des4;;
;;
;;
;;
SIM_R1_Scenario4_FB_Header;Reto Comercial 4: Consecuencias;
;;
SIM_R1_Scenario4_FB_Solution;C;
;;
SIM_R1_Scenario4_FB_Opt1_Title;;
SIM_R1_Scenario4_FB_Opt1_Text;Captas una oportunidad interesante, pero te desvías del foco inicial sin construir suficiente contexto emocional ni incluir a los tomadores clave. Aunque parezca estratégico, corres el riesgo de apresurar la decisión y perder credibilidad cuando no se concreta por falta de alineación con su esposa.;
;;
    SIM_R1_Scenario4_Opt1_KPI1;0;
    SIM_R1_Scenario4_Opt1_KPI2;0;
    SIM_R1_Scenario4_Opt1_KPI3;0;
    SIM_R1_Scenario4_Opt1_KPI4;-2;
;;
SIM_R1_Scenario4_FB_Opt2_Title;;
SIM_R1_Scenario4_FB_Opt2_Text;Práctico y comercial, pero táctico. Te centras en cerrar rápido una solución sin validar el panorama completo. El cliente podría avanzar, pero sin una visión clara de largo plazo. A futuro, pierde confianza y traslada sus planes a otro banco. Ganas la venta, pierdes la relación.;
;;
    SIM_R1_Scenario4_Opt2_KPI1;0;
    SIM_R1_Scenario4_Opt2_KPI2;1;
    SIM_R1_Scenario4_Opt2_KPI3;0;
    SIM_R1_Scenario4_Opt2_KPI4;-1;
;;
SIM_R1_Scenario4_FB_Opt3_Title;;
SIM_R1_Scenario4_FB_Opt3_Text;Excelente enfoque. Exploras el contexto relacional y emocional detrás de la decisión, lo que refuerza tu rol como asesor de confianza. Puede parecer que se retrasa la venta, pero en realidad estás construyendo principalidad y lealtad a largo plazo. El cliente involucra a su esposa en la decisión.;
;;
    SIM_R1_Scenario4_Opt3_KPI1;2;
    SIM_R1_Scenario4_Opt3_KPI2;0;
    SIM_R1_Scenario4_Opt3_KPI3;1;
    SIM_R1_Scenario4_Opt3_KPI4;-3;
;;
SIM_R1_Scenario4_FB_Opt4_Title;;
SIM_R1_Scenario4_FB_Opt4_Text;;
    SIM_R1_Scenario4_Opt4_KPI1;0;
    SIM_R1_Scenario4_Opt4_KPI2;0;
    SIM_R1_Scenario4_Opt4_KPI3;0;
    SIM_R1_Scenario4_Opt4_KPI4;0;
;;
;;
;;
;;
;;
SIM_R1_Scenario5_Header;Reto Comercial 5;
SIM_R1_Scenario5_Title;La hija de Mario se va a la maestría;
SIM_R1_Scenario5_Text;"Acabando de atender a Liliana, recibes una llamada familiar. Es Mario Araujo, cliente leal que ya has asesorado. Conoces bien a Mario: tiene una Tarjeta de Crédito y una inversión significativa con ScotiaNova. Siempre es un placer hablar con él, ya que suele platicar mucho contigo.<br><br>Hoy, Mario tiene una noticia importante: su hija ha sido aceptada en una prestigiosa universidad para realizar su maestría, y los costos de matrícula y otros gastos están a la vuelta de la esquina. Mario no quiere descapitalizar sus inversiones, pero tampoco quiere dejar de apoyar a su hija, por lo que está buscando un préstamo personal que le permita manejar estos costos sin afectar su estabilidad financiera. Es tu día de suerte: ¡otro cliente que sabe lo que quiere y lo quiere ya!<br><br><b>¿Qué le respondes a Mario?</b>";
SIM_R1_Scenario5_Img;/images/scenarios/R1scenario5.jpg;
;;
SIM_R1_Scenario5_Instructions;;
;;
SIM_R1_Scenario5_Question;¿Qué le respondes a Mario?;
SIM_R1_Scenario5_Opt1;No te dejas llevar por la información que ya tienes;
SIM_R1_Scenario5_Des1;Aunque ya conoces muy bien a Mario, y sabes que el producto se alinea con su necesidad, has aprendido de la situación con Liliana. Decides hacer preguntas para entenderle más a fondo, aunque esto ponga en riesgo la venta inmediata. <em>UTs: -1.</em>;
;;
SIM_R1_Scenario5_Opt2;Aceleras la venta;
SIM_R1_Scenario5_Des2;Aunque la conversación con Mario ha sido corta, lo conoces de mucho tiempo atrás y puedes ver en Counselor que este producto es perfecto para su necesidad. Sin mayores rodeos, decides iniciar el proceso de contratación del préstamo. <em>UTs: -1.</em>;
;;
SIM_R1_Scenario5_Opt3;Ofreces las joyas de la corona;
SIM_R1_Scenario5_Des3;Decides aprovechar la oportunidad para no solo satisfacer la necesidad inmediata de Mario, sino también para consolidar tu relación con él. Al terminar la conversación sobre el préstamo, le cuentas de una campaña que acaba de lanzar el banco para la nueva Tarjeta de Crédito Reserve y la oferta tan interesante que tienen de créditos hipotecarios, por su nivel adquisitivo estás seguro de que le interesará <em>UTs: -2.</em>;
;;
SIM_R1_Scenario5_Opt4;;
SIM_R1_Scenario5_Des4;;
;;
;;
;;
SIM_R1_Scenario5_FB_Header;Reto Comercial 5: Consecuencias;
;;
SIM_R1_Scenario5_FB_Solution;B;
;;
SIM_R1_Scenario5_FB_Opt1_Title;;
SIM_R1_Scenario5_FB_Opt1_Text;Aunque cierras la venta, Mario se sorprende por tus preguntas y siente que te tiene que explicar todo por segunda vez. Te das cuenta de que ya tenías clara la conexión entre su perfil y necesidad, no había que darle más vueltas.;
;;
    SIM_R1_Scenario5_Opt1_KPI1;2;
    SIM_R1_Scenario5_Opt1_KPI2;1;
    SIM_R1_Scenario5_Opt1_KPI3;0;
    SIM_R1_Scenario5_Opt1_KPI4;-1;
;;
SIM_R1_Scenario5_FB_Opt2_Title;;
SIM_R1_Scenario5_FB_Opt2_Text;Mario se siente satisfecho con la eficiencia del servicio, lo que refuerza su lealtad al banco y mejora su relación contigo como asesor.;
;;
    SIM_R1_Scenario5_Opt2_KPI1;2;
    SIM_R1_Scenario5_Opt2_KPI2;1;
    SIM_R1_Scenario5_Opt2_KPI3;0;
    SIM_R1_Scenario5_Opt2_KPI4;-1;
;;
SIM_R1_Scenario5_FB_Opt3_Title;;
SIM_R1_Scenario5_FB_Opt3_Text;Aunque cierras la venta, Mario se siente abrumado con tanta información y productos adicionales que no necesita en este momento. Enojado te comenta: "Ya tengo una TDC con ustedes, tal vez en el futuro podemos analizar una segunda, pero ahora siento que me vendes por vender.";
;;
    SIM_R1_Scenario5_Opt3_KPI1;2;
    SIM_R1_Scenario5_Opt3_KPI2;1;
    SIM_R1_Scenario5_Opt3_KPI3;0;
    SIM_R1_Scenario5_Opt3_KPI4;-2;
;;
;;
;;
;;
;;
SIM_R1_Scenario6_Header;Reto Comercial 6;
SIM_R1_Scenario6_Title;Dándole seguimiento a tus clientes y prospectos;
SIM_R1_Scenario6_Text;"Es viernes por la tarde y estás organizando tu agenda para la próxima semana, con el objetivo de mantener un tubo de ventas saludable. Mientras analizas varios perfiles de clientes y prospectos, te encuentras con Liliana Torres, una cliente a la que ayudaste hace tres meses a contratar un crédito para su negocio. Te percatas que Liliana ha liquidado su crédito y no ha realizado ningún movimiento adicional en su cuenta desde entonces. No te parece extraño, ya que ella ni era cliente de ScotiaNova hace 3 meses, probablemente liquidó su deuda con nosotros, y listo.<br><br><b>Ahora te enfrentas a una decisión interesante: ¿Deberías seguir enfocando tu tiempo en prospectar nuevos clientes o sería mejor desarrollar una estrategia para contactar a Liliana y vincularla con ScotiaNova?</b>";
SIM_R1_Scenario6_Img;/images/scenarios/R1scenario6.jpg;
;;
SIM_R1_Scenario6_Instructions;;
;;
SIM_R1_Scenario6_Question;¿Deberías seguir enfocando tu tiempo en prospectar nuevos clientes o sería mejor desarrollar una estrategia para contactar a Liliana y vincularla con ScotiaNova?;
SIM_R1_Scenario6_Opt1;Decides explorar nuevos horizontes;
SIM_R1_Scenario6_Des1;Optas por ampliar tu cartera de clientes aventurándote en la búsqueda de nuevos prospectos. Crees que Liliana ha encontrado su camino con otro banco, así que abres tus horizontes para mantener tu tubo de ventas lleno y así no dedicarles tiempo a causas perdidas. <em>UTs: -2.</em>;
;;
SIM_R1_Scenario6_Opt2;Activas la operación ¡Recuperando a Liliana!;
SIM_R1_Scenario6_Des2;Decides dedicar tiempo para crear una estrategia de reconexión y para entender por qué no ha habido movimientos en su cuenta. Además, haces un análisis de tus clientes clave del sector PREMIUM y PyME que llevan un par de meses inactivos para aplicar esta misma estrategia. <em>UTs: -1.</em>;
;;
SIM_R1_Scenario6_Opt3; ;
SIM_R1_Scenario6_Des3; ;
;;
SIM_R1_Scenario6_Opt4;;
SIM_R1_Scenario6_Des4;;
;;
;;
;;
SIM_R1_Scenario6_FB_Header;Reto Comercial 6: Consecuencias;
;;
SIM_R1_Scenario6_FB_Solution;B;
;;
SIM_R1_Scenario6_FB_Opt1_Title;;
SIM_R1_Scenario6_FB_Opt1_Text;Mantienes tu tubo de ventas activo y diversificado, lo que es crucial para el crecimiento sostenido de ScotiaNova. Sin embargo, pierdes la oportunidad de recuperar a una cliente con potencial como Liliana y su negocio. Además, dejaste espacio para que otros clientes también se vayan en el futuro.;
;;
    SIM_R1_Scenario6_Opt1_KPI1;1;
    SIM_R1_Scenario6_Opt1_KPI2;0;
    SIM_R1_Scenario6_Opt1_KPI3;0;
    SIM_R1_Scenario6_Opt1_KPI4;-2;

;;
SIM_R1_Scenario6_FB_Opt2_Title;;
SIM_R1_Scenario6_FB_Opt2_Text;"Logras reconectar con Liliana y te cuenta que tuvo una mala experiencia en otra sucursal de ScotiaNova, pero agradece tu seguimiento personalizado y asesoría. Le ayudas a solucionar ese trago amargo y al final Liliana es más consciente de nuestros beneficios. Además, tu estrategia no solamente sirvió con ella, sino también con otros clientes.";
;;
    SIM_R1_Scenario6_Opt2_KPI1;0;
    SIM_R1_Scenario6_Opt2_KPI2;1;
    SIM_R1_Scenario6_Opt2_KPI3;2;
    SIM_R1_Scenario6_Opt2_KPI4;-1;

;;
SIM_R1_Scenario6_FB_Opt3_Title;;
SIM_R1_Scenario6_FB_Opt3_Text;" ";

    SIM_R1_Scenario6_Opt3_KPI1;0;
    SIM_R1_Scenario6_Opt3_KPI2;0;
    SIM_R1_Scenario6_Opt3_KPI3;0;
    SIM_R1_Scenario6_Opt3_KPI4;0;

;;
SIM_R1_Scenario6_FB_Opt4_Title;;
SIM_R1_Scenario6_FB_Opt4_Text;;
    SIM_R1_Scenario6_Opt4_KPI1;0;
    SIM_R1_Scenario6_Opt4_KPI2;0;
    SIM_R1_Scenario6_Opt4_KPI3;0;
    SIM_R1_Scenario6_Opt4_KPI4;0;
;;
;;
;;
SIM_R1_Scenario7_Header;Reto Comercial 7;
SIM_R1_Scenario7_Title;Cerrando la venta;
SIM_R1_Scenario7_Text;"Recibes una llamada de Carlos Puyol, hace unas semanas hablaste con él sobre un seguro de vida que necesita contratar para él y su familia. Hoy te comenta, que después de consultarlo con su esposa, ha decidido seguir adelante con el proceso de contratación. Menciona que ha leído todos los beneficios, la letra chiquita (términos y condiciones), e incluso comparó el producto con otros bancos. Bromea que ya se siente experto en el tema, aunque obviamente no entiende a la perfección todo el nivel de detalle.<br><br>Sabes que es común no entender todos los detalles técnicos, como cuando uno trata de decodificar la letra chiquita de otros productos o servicios.<br><br><b>Tu objetivo es avanzar garantizando la venta y manteniendo el alto nivel de servicio de ScotiaNova. ¿Cómo lo haces?</b>";
SIM_R1_Scenario7_Img;/images/scenarios/R1scenario7.jpg;
;;
SIM_R1_Scenario7_Instructions;;
;;
SIM_R1_Scenario7_Question;Tu objetivo es avanzar garantizando la venta y manteniendo el alto nivel de servicio de ScotiaNova. ¿Cómo lo haces?;
SIM_R1_Scenario7_Opt1;El "ABC" del seguro;
SIM_R1_Scenario7_Des1;Aunque Carlos se siente cómodo con su nivel de conocimiento del producto, tomas la iniciativa de aclarar los términos y condiciones, de manera que pueda comprender cómo se aplican a su situación específica. Utilizas ejemplos prácticos y analogías con situaciones cotidianas para facilitar su comprensión. <em>UTs: -1.</em>;
;;
SIM_R1_Scenario7_Opt2;El respaldo de ScotiaNova;
SIM_R1_Scenario7_Des2;Te vas por la opción más práctica para él y le muestras cómo utilizar todas las opciones disponibles en ScotiaNova para resolver sus dudas en el futuro: llamar al contact center y la sección de preguntas frecuentes en la app y sitio web. Le aseguras que siempre tendrá apoyo disponible. <em>UTs: -1.</em>;
;;
SIM_R1_Scenario7_Opt3;La vía express del asesor;
SIM_R1_Scenario7_Des3;Carlos es un Director muy reconocido y ocupado, sabes que su tiempo es valioso, así que decides avanzar con el proceso de contratación. Finalizas la llamada diciéndole: "Señor Carlos, ya tiene mi WhatsApp. Como siempre, siéntase en la confianza de escribirme en cualquier momento y con mucho gusto le daré el servicio que se merece". <em>UTs: 0.</em>;
;;
SIM_R1_Scenario7_Opt4;;
SIM_R1_Scenario7_Des4;;
;;
;;
;;
SIM_R1_Scenario7_FB_Header;Reto Comercial 7: Consecuencias;
;;
SIM_R1_Scenario7_FB_Solution;A;
;;
SIM_R1_Scenario7_FB_Opt1_Title;;
SIM_R1_Scenario7_FB_Opt1_Text;Carlos se sorprende porque había malentendido un par de términos y condiciones, se alegra de que se los explicaste ahora. Su esposa, escuchando por el altavoz se alegra por tu nivel de servicio y te dice que te va a ir a visitar porque quiere abrir una cuenta desde hace tiempo.;
;;
    SIM_R1_Scenario7_Opt1_KPI1;2;
    SIM_R1_Scenario7_Opt1_KPI2;1;
    SIM_R1_Scenario7_Opt1_KPI3;0;
    SIM_R1_Scenario7_Opt1_KPI4;-1;
;;
SIM_R1_Scenario7_FB_Opt2_Title;;
SIM_R1_Scenario7_FB_Opt2_Text;A Carlos le gusta la idea y el servicio omnicanal del banco, te agradece las atenciones.;
;;
    SIM_R1_Scenario7_Opt2_KPI1;2;
    SIM_R1_Scenario7_Opt2_KPI2;1;
    SIM_R1_Scenario7_Opt2_KPI3;0;
    SIM_R1_Scenario7_Opt2_KPI4;-1;
;;
SIM_R1_Scenario7_FB_Opt3_Title;;
SIM_R1_Scenario7_FB_Opt3_Text;Carlos aprecia la agilidad, pero puede que algunos detalles se le escapen. Aunque avanza el proceso, no se reforzó la comprensión ni el vínculo tanto como en otras opciones.;
;;
    SIM_R1_Scenario7_Opt3_KPI1;2;
    SIM_R1_Scenario7_Opt3_KPI2;1;
    SIM_R1_Scenario7_Opt3_KPI3;0;
    SIM_R1_Scenario7_Opt3_KPI4;0;
;;
;;
;;
;;
;;
;;
GD_SIM_R1_Finish;Finalizar o pausar Ronda 1;
GD_SIM_R1_Finish_Modal;"<span class="highlight">¿Estás seguro de que quieres terminar o pausar la ronda?</span><br><br>Por favor, asegúrate antes de que todos los participantes han finalizado esta parte de la simulación.";
;;
SIM_R1_Pause_Header;Lunch break;
SIM_R1_Pause_Title;Has terminado la primera ronda de decisiones. Por favor avisa al facilitador de BTS.;
SIM_R1_Pause1_Title;Has terminado la segunda ronda de decisiones. Por favor avisa al facilitador de BTS.;
SIM_R1_Pause_Img;/images/scenarios/sim_round_finish.jpg;
;;
SIM_R1_Finish_Header;Fin de Ronda 1;
SIM_R1_Finish_Title;¡Felicidades! Has finalizado la simulación.;
SIM_R1_Finish_Text_Good;Por favor, avisa al facilitador de BTS.;
SIM_R1_Finish_Text_Bad;Aunque tuviste una gestión exitosa, utilizaste un número de UTs mayor al que tenías disponible, esto hizo que muchos colaboradores trabajaran tiempo adicional, por lo que tu Pulso bajó un poco. Por favor, avisa al facilitador de BTS.;
SIM_R1_Finish_Img;/images/scenarios/sim_round_finish.jpg;
;;
SIM_R1_Summary_Header;Resumen - Ronda 1;
SIM_R1_Summary_Instructions;A continuación, se muestra un resumen de la puntuación obtenida para el desarrollo de cada uno de los miembros del equipo:;
;;
;;
;;
GD_Extra_OpenQuestion1_Input;Pregunta Abierta #1;
GD_Extra_OpenQuestion2_Input;Pregunta Abierta #2;
GD_Extra_OpenQuestion3_Input;Pregunta Abierta #3;
;;
Extra_OpenQuestion1_Input_Header;Pregunta abierta;
Extra_OpenQuestion1_Input_Instructions;Escriban las mejores prácticas para prospectar.;
;;
Extra_OpenQuestion1_Input_Placeholder;Mi respuesta es...;
;;
Extra_OpenQuestion2_Input1_Header;Pregunta abierta - Ejercicio en equipos, conversaciones ROPE;
Extra_OpenQuestion2_Input1_Instructions;Definir 3 preguntas que le hubieran hecho a Liliana (que letra son).;
;;
Extra_OpenQuestion2_Input1_Placeholder;Mi respuesta es...;
;;
Extra_OpenQuestion2_Input2_Header;Pregunta abierta;
Extra_OpenQuestion2_Input2_Instructions;Definir 3 preguntas que le hubieran hecho a Mario (que letra son).;
;;
Extra_OpenQuestion2_Input2_Placeholder;Mi respuesta es...;
;;
Extra_OpenQuestion3_Input_Header;Pregunta abierta - 3 Preguntas poderosas;
Extra_OpenQuestion3_Input_Instructions;Escriban aquí las preguntas poderosas.;
;;
Extra_OpenQuestion3_Input_Placeholder;Mi respuesta es...;
;;
;;
;;




;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;


Participants:;KeypadId;Email;Language;Expiry;Password;Privileges;CurrentActionControl;GD;GD_Foreman;Follower;Team;Team_Foreman;Filter_Agg;

Facilitator;;scotia_sim_FAC;en;;scotia%0;Vote,GroupDirector;Individual:LandingPage_FAC;1;1;;;;;

Team 0;;scotia_sim_0;en;;b;Vote;ForemanOrDirector;1;;2;0;0;;

Team 1;;scotia_sim_1;en;;scotia%0;Vote;ForemanOrDirector;1;;0;1;1;1;
Team 1 - follower;;scotia_sim_1f;en;;scotia%0;Vote;ForemanOrDirector;1;;1;1;;;
Team 2;;scotia_sim_2;en;;scotia%0;Vote;ForemanOrDirector;1;;0;2;2;1;
Team 2 - follower;;scotia_sim_2f;en;;scotia%0;Vote;ForemanOrDirector;1;;1;2;;;
Team 3;;scotia_sim_3;en;;scotia%0;Vote;ForemanOrDirector;1;;0;3;3;1;
Team 3 - follower;;scotia_sim_3f;en;;scotia%0;Vote;ForemanOrDirector;1;;1;3;;;
Team 4;;scotia_sim_4;en;;scotia%0;Vote;ForemanOrDirector;1;;0;4;4;1;
Team 4 - follower;;scotia_sim_4f;en;;scotia%0;Vote;ForemanOrDirector;1;;1;4;;;
Team 5;;scotia_sim_5;en;;scotia%0;Vote;ForemanOrDirector;1;;0;5;5;1;
Team 5 - follower;;scotia_sim_5f;en;;scotia%0;Vote;ForemanOrDirector;1;;1;5;;;
Team 6;;scotia_sim_6;en;;scotia%0;Vote;ForemanOrDirector;1;;0;6;6;1;
Team 6 - follower;;scotia_sim_6f;en;;scotia%0;Vote;ForemanOrDirector;1;;1;6;;;
Team 7;;scotia_sim_7;en;;scotia%0;Vote;ForemanOrDirector;1;;0;7;7;1;
Team 7 - follower;;scotia_sim_7f;en;;scotia%0;Vote;ForemanOrDirector;1;;1;7;;;
Team 8;;scotia_sim_8;en;;scotia%0;Vote;ForemanOrDirector;1;;0;8;8;1;
Team 8 - follower;;scotia_sim_8f;en;;scotia%0;Vote;ForemanOrDirector;1;;1;8;;;
Team 9;;scotia_sim_9;en;;scotia%0;Vote;ForemanOrDirector;1;;0;9;9;1;
Team 9 - follower;;scotia_sim_9f;en;;scotia%0;Vote;ForemanOrDirector;1;;1;9;;;
Team 10;;scotia_sim_10;en;;scotia%0;Vote;ForemanOrDirector;1;;0;10;10;1;
Team 10 - follower;;scotia_sim_10f;en;;scotia%0;Vote;ForemanOrDirector;1;;1;10;;;


Team 1;;scotia_sim_1_test;en;;scotia%0;Vote;ForemanOrDirector;1;;0;11;11;1;
Team 1 - follower;;scotia_sim_1f_test;en;;scotia%0;Vote;ForemanOrDirector;1;;1;11;;;
Team 2;;scotia_sim_2_test;en;;scotia%0;Vote;ForemanOrDirector;1;;0;12;12;1;
Team 2 - follower;;scotia_sim_2f_test;en;;scotia%0;Vote;ForemanOrDirector;1;;1;12;;;
Team 3;;scotia_sim_3_test;en;;scotia%0;Vote;ForemanOrDirector;1;;0;13;13;1;
Team 3 - follower;;scotia_sim_3f_test;en;;scotia%0;Vote;ForemanOrDirector;1;;1;13;;;


Control Room;0;admin;;;;Administrator,GroupPrivileges;Director;
Joshua Duffill;;<EMAIL>;;;;Vote,Administrator,GroupPrivileges;Individual;
Liliana Puente;;<EMAIL>;;;;Vote,Administrator,GroupPrivileges;Individual;
Gabriel Oliveira;;<EMAIL>;;;;Vote,Administrator,GroupPrivileges;Individual;
Pei Wen Low;;<EMAIL>;;;;Vote,Administrator,GroupPrivileges;Individual;
Cristian Rossato;;<EMAIL>;;;;Vote,Administrator,GroupPrivileges;Individual;
Antonio Gonzalez;;<EMAIL>;;;;Vote,Administrator,GroupPrivileges;Individual;


Subsystems:;type
Syntase.Subsystem.Editor, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null;Syntase.Subsystem.Editor.EditorSystem
Syntase.Subsystem.Ranking, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null;Syntase.Subsystem.Ranking.RankingSystem
Wizer.Subsystem.Search, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null;Wizer.Subsystem.Search.SearchSystem
Wizer.Subsystem.Cache, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null;Wizer.Subsystem.Cache.CacheSystem
