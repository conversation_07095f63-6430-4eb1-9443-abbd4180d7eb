/* 
    "Plain Vanilla" wizlet that takes a template and some data as the only input
    and renders the template with functionality from wizletBase, and nothing else.
*/

define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT'], function ($, Q, WizerApi, WizletBase, doT) {

    var Vanilla = function () {
        this.type = 'Vanilla';
        this.level = 1;
    };

    Vanilla.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;

        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];
        // if (wizletInfo.templateInEvent) {
        //     requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);
        // }
        // else {// either 1 from templateInEvent or template should be present
        //     if (wizletInfo.template.toLowerCase().indexOf(wizerApi.eventName().toLowerCase() + "/") == -1 && wizletInfo.template.toLowerCase().indexOf("/wizer/content/wizard/") == -1 && wizletInfo.template.toLowerCase().indexOf('vanilla/test') == -1) {
        //         // if template is defined in core, load it from theme folder
        //         var templateName = wizletInfo.template.split("/").pop();
        //         requirements.push('doT!' + wizletInfo.templatePath + templateName);
        //     } else {
        //         requirements.push('doT!' + wizletInfo.template);
        //     }

        // }
        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });
    };

    Vanilla.prototype.unloadHandler = function () {
        //unload wizletbase
        WizletBase.unloadHandler({ wizlet: this });
    };

    Vanilla.prototype.render = function (options) {
        var self = this;
        var leaderOption_R1;
        var leaderOption_R2;
        var isFollower = false;
        var userEmail = "";

        var qId_R1_Init6 = self.wizerApi.getQuestionIdByName("Q_SIM_R1_Initiatives_6");
        var qId_R2_Init5 = self.wizerApi.getQuestionIdByName("Q_SIM_R2_Initiatives_5");
        var qId_Follower = self.wizerApi.getQuestionIdByName("Follower");
        self.wizerApi.getMyVotes([qId_Follower]).then((res) => {
            console.log(res)
            isFollower = Boolean(Number(res.votes[qId_Follower][0]));
            userEmail = res.participantEmail + "f";

        }).then((value) => {
            console.log("is the user follower: ", isFollower);
           
            if(!isFollower){
                self.wizerApi.getMyVotes([qId_R1_Init6]).then((res) => {
                    if(res.votes[qId_R1_Init6][0] != undefined){
                        leaderOption_R1 = res.votes[qId_R1_Init6][0];
                        console.log("leader choose for R1: ", leaderOption_R1);

                          //get vote from leader and add to follower
                          AjaxGetJson('Vote', 'Assess' + '?question=' + "Q_SIM_R1_Initiatives_6" + '&vote=' + leaderOption_R1 + '&email=' + userEmail).then(function (response) {
                            console.log("✅ vote added R1")
                        });  
                    }
                }).then((value) => {
                       
                }).then((value) => {
                    self.wizerApi.getMyVotes([qId_R2_Init5]).then((res) => {
                        if(res.votes[qId_R2_Init5][0] != undefined){
                            leaderOption_R2 = res.votes[qId_R2_Init5][0];
                            console.log("leader choose for R2: ", leaderOption_R2);
                            
                            console.log(leaderOption_R2)
                                //get vote from leader and add to follower
                            AjaxGetJson('Vote', 'Assess' + '?question=' + "Q_SIM_R2_Initiatives_5" + '&vote=' + leaderOption_R2 + '&email=' + userEmail).then(function (response) {
                                console.log("✅ vote added R2 : ", leaderOption_R2)
                            });  
                        }
                    })  
                }).then((value) => {
                    
                });
            }
            

        })


   


        return self.templateDefer.promise.then(function (template) {
            // var fragment = template(options.wizletInfo);
            // options.context.html(fragment);
            return true;
        })
        .fail(this.wizerApi.showError)
    };

    Vanilla.getRegistration = function () {
        return new Vanilla();
    };

    return Vanilla;

});
