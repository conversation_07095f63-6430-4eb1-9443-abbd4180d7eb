
<div class="container{{?it.animate}} animate__animated animate__{{=it.animate}}{{?}}"> 

    <section class="psn cl-component">

        
        {{?it.header}}<h4 class="header">{{=it.header}}</h4>{{?}}   
        {{?it.subheader}}<h5 class="header subtitle">{{=it.subheader}}</h5>{{?}}
        {{?it.instructions}}<h6 class="instructions flow-text">{{=it.instructions}}</h6>{{?}}


        <div class="psn-container cl-container">
            <div class="cl-xs-column-12 cl-row">
                {{? it.intro}}
                <div class="psn-intro cl-instructions">
                    <p class="psn-intro__text cl-instructions__text">{{=it.intro}}</p>
                </div>
                {{?}}
                <div class="psn-content cl-content">
                    <article class="psn-thumbnails psn-article cl-article">
                        {{~it.characters :character:index}}
                        <div class="psn-thumbnail{{=index}} psn-thumbnail" data-nav-button-fixed data-nav-button-{{=index}} data-buttonnumber="{{=index}}">
                            <div class="psn-thumbnail_icon" style="background-image:url('Wizer/Pages/Events/{{=wizerApi.eventName()}}/{{=character.image}}')"></div>
                            <div class="psn-thumbnail_name">{{=character.name}}</div>
                        </div>
                        {{~}}
                    </article>
                    {{?it.meetingText}}
                    <div class="psn-meeting_Count"></div>
                    {{?}}
                    {{var infoLength = it.charactersInfo.length;}}
                    <article class="psn-infoContainer psn-article cl-article" style="width: {{=infoLength*100}}%">
                        {{~it.charactersInfo :info:ci}}
                        <div class="psn-info psn-info-{{=ci}}" style="width: {{=100/infoLength}}%">
                            <div class="psn-info_Wrapper">
                                <div class="card hoverable horizontal">
                                    {{?info.image && !info.image.embed && (info.image.position=="left" || !info.image.position)}}
                                    <div class="card-image left hide-on-small-and-down">
                                        <img class="psn-info_image {{?info.image.size}}{{=info.image.size}}{{?}}" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=info.image.src}}" />
                                    </div>
                                    {{?}}
                                    <div class="card-content row">
                                        {{?info.title}}<span class="card-title">{{=info.title}}</span>{{?}}                                    
                                        {{?info.image && info.image.embed}}
                                            <img class="psn-info_image hide-on-small-and-down embed {{?info.image.position}}{{=info.image.position}}{{?}} {{?info.image.size}}{{=info.image.size}}{{?}}" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=info.image.src}}" />
                                        {{?}}
                                        <span class="flow-text">{{=info.text}}</span>
                                    </div>
                                    {{?info.image && !info.image.embed && info.image.position=="right"}}
                                    <div class="card-image right hide-on-small-and-down">
                                        <img class="psn-info_image {{?info.image.size}}{{=info.image.size}}{{?}}" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=info.image.src}}" />
                                    </div>
                                    {{?}}
                                </div>
                            </div>
                        </div>
                        {{~}}
                    </article>
                </div>
            </div>
        </div>
    </section>
    
</div>