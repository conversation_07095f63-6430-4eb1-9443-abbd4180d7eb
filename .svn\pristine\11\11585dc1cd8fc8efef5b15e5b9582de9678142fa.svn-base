@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
// @import "../materialize-src/sass/components/variables";
@import "mixins";


.wizlet.wizletPostits {
   
    .container {
        // width: 90%;

        .droppable-container {
                        
            img {
                object-fit: contain;
                width: 100%;
                //max-height: 70vh;
            }

            &.wider {
                width: 120%;
                margin-left: -10%;
            }

            #droppable-container {
                padding: 0;
            }
            &.disabled {
                pointer-events: none;
            }
            &.solution,
            &.positioned {
                #droppable-container {
                    width: 90% !important;
                    margin: 0 5% 20px 5%;
                }
                .draggable-container-left,
                .draggable-container-center,
                .draggable-container-right {
                    // position: absolute;
                    height: 0 !important;
                }
            }
            &.solution {
                pointer-events: none;
            }
            

            .draggable-row {
                &.stacked { 
                    position: absolute;
                }

                .draggable {
                    // width: 90%;
                    min-height: 10vw;
                    background-size: 100% 100%;
                    background-position: center;
                    background-repeat: no-repeat;
                    background-color: transparent;
                    cursor: -webkit-grab;
                    border: none;
                    box-shadow: none;
                    margin: 0;
                    z-index: 1 !important;
  
                    &.bounce {
                        @include animation-name(bounceInfinite);
                        @include animation-duration(5s);
                        @include animation-iteration-count(infinite);
                        @include animation-timing-function(ease);
                        @include animation-fill-mode(forwards);
                    }

                    i.check-icon {
                        position: absolute;
                        z-index: 1;
                        &.scaled-out {
                            transform: scale(0);
                        }
                        @media #{$small-and-down} { font-size: 3rem; }
                    }

                    &.rotated { 
                        &.rotatedLeft { 
                            background-image: url("../images/postits/positL.png");
                            padding: 25px 20px 10px 15px;
                            .text {
                                transform: rotate(-2deg);
                            }
                            i.check-icon {    
                                &.left {
                                    left: -5px;
                                    top: 5px;
                                }
                                &.right {
                                    right: 0;
                                }
                            }
                            
                        }
                        &.rotatedRight { 
                            background-image: url("../images/postits/positR.png");
                            padding: 25px 10px 10px 20px;
                            .text {
                                transform: rotate(+2deg);
                            }
                            i.check-icon {    
                                &.left {
                                    left: 2px;
                                    top: 0px;
                                }
                                &.right {
                                    right: 0;
                                }
                            }
        
                        }
                    }

                    &:not(.rotated) {
                        padding: 5px;
                        border: outset 5px color("client-colors", "border");
                        i.check-icon {    
                            top: -15px;
                            right: -15px;
                            margin: 0;
                            background-color: color("client-colors", "white");
                            border-radius: 50%;
                        }
                    }

                    &.empty {
                        background: none;
                    }
                    
                    &.ui-state-disabled {
                        opacity: 1;
                    }
                    
                    .text {
                        font-size: 100%;
                        // @media #{$medium-and-down} { font-size: 60%; }
                        // @media #{$medium-and-up} { font-size: 70%; }
                        // @media #{$large-and-up} { font-size: 75%; }
                        // @media #{$extra-large-and-up} { font-size: 100%; }
                    }

                    

                }
            }

            &.solution,
            &.positioned {
                .draggable-row {
                    position: absolute;
                    width: 18%;
                }
            }

            .slots-container {
                
                .header {
                    color: color("client-colors", "font3");
                }

                .slot.wrapper { 
                    // height: 50vh;
                    width: 100%;
                    margin: 0;
                    padding: 10px;
                    background-size: 100% 100%;
                    background-position: center;
                    background-repeat: no-repeat;
                    background-image: url("../images/postits/whiteBoard.png");

                    .slot.droppable { 
                        height: 100%;
                        width: 100%;
                        background-size: 90% 90%;
                        background-repeat: no-repeat;
                        background-position: center;
                        opacity: 0.75;
                    }
                }
                
                &.bold >.header { font-weight: bold; }
                &.title-color {
                    &.color-green >.header { color: color("client-colors", "green"); }
                    &.color-red >.header { color: color("client-colors", "red"); }                    
                }   


                // &.board1 .slot.droppable { 
                //     background-image: url("../images/postits/negocio.png");
                // }
                // &.board2 .slot.droppable { 
                //     background-image: url("../images/postits/personas.png");
                // }
                // &.board3 .slot.droppable { 
                //     background-image: url("../images/postits/colaboracion.png");
                // }
            }

            &.absolute_position {

                position: relative;
                &:not(.with_grid) {
                    width: 100% !important;
                    margin: 0;
                }
                
                >section {
                    position: absolute;
                }
                #droppable-container {
                    width: 100% !important;
                    height: 100%;
                    top: 0;
                    left: 0;
                    padding: 0;
                    margin: 0;

                    >section {
                        position: absolute;                        
                        margin: 0;
                        padding: 0;

                        .draggable-row {
                            width: 100%; height: 100%;
                            position: absolute; 
                            top: 0;
                            margin: 0;
                            padding: 0;
                            .draggable {
                                min-height: auto;
                                text-align: center;
                                height: inherit; width: inherit;
                                padding: 0;
                                img {
                                    width: 100%; height: 100%;
                                }
                            }
                        }
                    }

                    .slots-container {
                        position: absolute;
                        margin: 0;
                        padding: 0;
                        .wrapper {
                            height: 100%;
                            padding: 0;
                        }
                    }
                }
                
                &.solution,
                &.positioned {
                    .draggable-row {
                        width: 100%;
                    }
                }

            }
            &.no_img_board {
                
                .slots-container {

                    .slot.wrapper { 
                        background: none;
                        // background-color: #ff000047;

                        .slot.droppable { 
                            height: 100%;
                            width: 100%;
                            background: none;
                        }
                    }
                    
                }

            }
            &.no_img_postit {
                
                .draggable-row {

                    .draggable {

                        background: none;
                        background-color: color("client-colors", "white");
    
                        &.rotated { 
                            background-image: none;
                            padding: 0;
                            .text {
                                transform: none;
                            }
                            i.check-icon {  
                                &.left {
                                    left: 0;
                                    top: 0;
                                }
                                &.right {
                                    right: 0;
                                }

                            }
                        }                       

                    }
                }                
            }

            &.transparent {
                padding-top: 30px;
                .draggable-row {
                    .draggable {
                        padding: 5px;
                        border: none;
                        background-color: transparent;
                    }
                }
            }
        }


        @include row-submit;
    }
}