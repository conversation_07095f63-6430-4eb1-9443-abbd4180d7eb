﻿
<div class="container{{?it.animate}} animate__animated animate__{{=it.animate}}{{?}}">

    {{?it.help && it.help.icon}}
    <div class="header-container">
        <h4 class="header help">{{=it.header}}</h4>        
        <i class="material-icons help client-colors-text text-primary {{?it.help.modalID}}modal-trigger" href="#{{=it.help.modalID}}{{?}}">{{=it.help.icon}}</i>
    </div>
    {{??}}
        {{?it.header}}<h4 class="header">{{=it.header}}</h4>{{?}}   
    {{?}}
    {{?it.subheader}}<h5 class="header subtitle">{{=it.subheader}}</h5>{{?}}
    {{?it.instructions}}<h6 class="instructions flow-text">{{=it.instructions}}</h6>{{?}}

    
    <div class="tabs-holder{{?it.verticalMode}} vertical-mode{{?}}{{?it.class}} {{=it.class}}{{?}}">
        <ul class="tabs tabs-fixed-width z-depth-1"> 
        {{?it.tabs}}
        {{~it.tabs :tab:idx}}
        <li class="tab {{?idx==it.tabs.length-1}}last{{?}}">
            {{?it.bind}}
                <a {{?it.tabPanelID}}href="#{{=it.tabPanelID}}{{=idx}}"{{??}}href="#tabPanel{{=idx}}"{{?}} id="tab{{=idx}}"
                    {{?(it.DB[it.bind]==(idx+1))}}
                        class="active my-option {{?it.correctOption==(idx+1)}}isCorrect{{?}}">
                        <span class="strong">{{=it.myText}}</span> - {{=tab}}
                    {{??}}
                        class="other {{?it.correctOption==(idx+1)}}{{? !it.DB[it.bind]}}active{{?}} isCorrect{{?}}">
                        <span class="">{{=tab}}</span>
                    {{?}}
                    
                    {{?it.isCheck}}
                    <i class="check-icon material-icons small right {{?it.correctOption==(idx+1)}}green-text{{??}}red-text{{?}}">
                        <!-- {{?it.correctOption==(idx+1)}}check{{??}}close{{?}} -->
                        {{?it.correctOption==(idx+1)}}check{{?}}
                    </i>
                    {{?}}
                </a>
            {{??}}
                {{?it.activeTab}}
                    {{?it.activeTabByVal}}
                    <a {{?it.tabPanelID}}href="#{{=it.tabPanelID}}{{=idx}}"{{??}}href="#tabPanel{{=idx}}"{{?}} {{?idx==it.activeTab}}class="activeTab"{{?}}>
                        <span class="strong">{{=tab}}</span>
                    </a>
                    {{??}}
                    <a {{?it.tabPanelID}}href="#{{=it.tabPanelID}}{{=idx}}"{{??}}href="#tabPanel{{=idx}}"{{?}} {{?idx==it.DB[it.activeTab]}}class="activeTab"{{?}}>
                        <span class="strong">{{=tab}}</span>
                    </a>
                    {{?}}
                {{??}}
                <a {{?it.tabPanelID}}href="#{{=it.tabPanelID}}{{=idx}}"{{??}}href="#tabPanel{{=idx}}"{{?}} {{?idx===0}}class="active"{{?}}>
                    {{?it.verticalMode}}
                        <span class="strong hide-on-small-and-down">{{=tab}}</span>
                        <span class="strong hide-on-med-and-up">{{=it.tabsVertical[idx]}}</span>
                    {{??}}
                        <span class="strong">{{=tab}}</span>
                    {{?}}
                </a>
                {{?}}
            {{?}}
        </li>
        {{~}}
        {{?}}
        </ul>
    </div>



</div>
        
