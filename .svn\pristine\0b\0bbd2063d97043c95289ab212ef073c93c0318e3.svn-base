
<div class="container {{?it.animate}}animate__animated animate__{{=it.animate}}{{?}}"
{{?it.id}}id="{{=it.id}}"{{?}} {{?it.isHidden}}hidden{{?}}> 
    <div>
        
        {{?it.help}}
        <div class="header-container">   
        {{?}}
    
        {{?it.header}}
        <h4 class="header {{?it.headerLabel}}with-label{{?}}{{?it.help}} help{{?}}">
            {{?it.headerLabel}}<span class="new badge header-label" data-badge-caption="{{=it.headerLabel}}"></span>{{?}}
            {{=it.header}}
        </h4>
        {{?}}
        {{?it.help}}
            <i class="material-icons help client-colors-text text-primary {{?it.help.modalID}}modal-trigger" href="#{{=it.help.modalID}}{{?}}">{{?it.help.icon}}{{=it.help.icon}}{{??}}help{{?}}</i>
         </div>   
        {{?}}

        {{?it.instructions}}<h6 class="instructions flow-text">{{=it.instructions}}</h6>{{?}}
        
        <div class="card">
            
            <div class="card-content">
                
                <!-- {{?it.title}}<h5 class="header title flow-text">{{=it.title}}</h5>{{?}} -->
                {{?it.title}}
                <h5 class="header title {{?it.titleLabel}}with-label{{?}}">
                    {{?it.titleLabel}}<span class="new badge header-label" data-badge-caption="{{=it.titleLabel}}"></span>{{?}}
                    {{=it.title}}
                </h5>
                {{?}}
                {{?it.subtitle}}<h6 class="header strong subtitle">{{=it.subtitle}}</h6>{{?}}
                
                
                {{?it.content}}
                    {{?it.content.title}}<span class="card-title">{{=it.content.title}}</span>{{?}}
                                
                    {{?it.content.img_embed && it.content.img_embed.src}}
                        <img class="embed {{?it.content.img_embed.position}}{{=it.content.img_embed.position}}{{?}}" {{?it.content.img_embed.style}}style="{{=it.content.img_embed.style}}"{{?}}
                        src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.content.img_embed.src}}" {{?it.content.img_embed.alt}}alt="{{=it.content.img_embed.alt}}"{{?}}/>
                    {{?}}                
        
                    {{?it.content.body}}
                        <span class="flow-text">{{=it.content.body}}</span>
                    {{?}}
                {{?}}
                
                <div class="row">
                    <form class="col s12" novalidate autocomplete="off">

                        {{?it.options}}
                        <ul {{?it.isInline}}class="inline row{{?it.isInlineClass}} {{=it.isInlineClass}}{{?}}"{{?}}>
                        {{~it.options :option:idx}}
                      
                            {{?option.header}}<h5 class="subheader">{{=option.header}}</h5>{{?}}

                            <li class="{{?it.class}}{{=it.class}}{{?}} {{?it.DB[it.isFollower]>0}}disabled{{?}}{{?it.isInline}} col {{=it.optionWidth}}{{?}}"
                                {{?option.preload && it.DB[option.preload] && it.DB[option.preload]=='1'}} disabled {{?}}
                                {{?option.hidden && (!it.isFollower || (it.DB[it.isFollower]==0)) }}
                                    {{?option.condition}}
                                        {{?!it.DB[option.condition] || (it.DB[option.condition]==option.condition_Val)}}
                                            hidden
                                        {{?}}
                                    {{??}}
                                        hidden
                                    {{?}}
                                {{?}}>


                                <div class="card-panel hoverable {{?!option.label || option.hideLabel}}hide-label{{?}}">
                                    <label class="row">
                                        {{? option.label && !option.hideLabel}}
                                        <span class="new badge question-label" data-badge-caption="{{=option.label}}"></span>
                                        {{?}}
                                        <input type="checkbox" class="{{=it.inputclass}}" 
                                            {{?it.DB[it.isFollower]>0}}
                                                disabled data-bind="{{=option.bind}}" data-correct="{{=option.isCorrect}}"
                                            {{??}}
                                                {{?option.preload && it.DB[option.preload] && it.DB[option.preload]=='1'}}checked{{??}} {{?option.bind}}data-bind="{{=option.bind}}"{{?}} {{?option.binding}}data-binding="{{=option.binding}}"{{?}} data-label="{{=option.label}}"{{?}} data-correct="{{=option.isCorrect}}"
                                            {{?}}/>
                                            
                                        <span class="title {{?option.titleClass}}{{=option.titleClass}}{{?}} {{?it.grid && option.image}}with-image{{?}}">
                                            
                                            {{?it.grid && option.image}}
                                                
                                                <div class="image col {{?it.grid.image}}{{=it.grid.image}}{{??}}s2{{?}}">
                                                    <img class="responsive-img option-image" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=option.image}}"/>
                                                </div>
                                                <div class="option col {{?it.grid.option}}{{=it.grid.option}}{{??}}s10{{?}} {{?option.imagePosition}}{{=option.imagePosition}}{{?}}">
                                            
                                                    {{??}}
                                            <div class="col s12">
                                            {{?}}
                                                {{=option.title}}
                                            </div>

                                        </span>
                                    </label>
                                    {{?it.isCheck}}
                                    <i class="check-icon material-icons small right {{?option.isCorrect}}green-text{{??}}red-text{{?}} scale-transition scaled-out">
                                        {{?option.isCorrect}}check_circle{{??}}cancel{{?}}
                                    </i>
                                    {{?}}
                                </div>

                                {{?option.moreInfo}}
                                <ul class="collapsible">   
                                    <li data-index="{{=idx+1}}" {{?it.descActive}}class="active"{{?}}>
                                        <div class="collapsible-header flow-text">
                                            <i class="expand material-icons large bottom">{{?it.descActive}}expand_less{{??}}expand_more{{?}}</i>
                                            <span class="strong">{{?it.descActive}}{{=it.lessInfo}}{{??}}{{=it.moreInfo}}{{?}}</span>
                                        </div>
                                        <div class="collapsible-body flow-text"><span>{{=option.moreInfo}}</span></div>
                                    </li>
                                </ul>
                                {{?}}
                                
                            </li> 

                        {{~}}
                        </ul>
                        {{?}}
                        
                    </form>
                </div>
            </div>

        </div>  
        
        {{?it.checkBtn}}
        <div class="row submit">
            <a id="checkBtn" hidden class="btn pulse client-colors button {{?it.checkBtn.animate}}animated {{=it.checkBtn.animate}}{{?}}">
                <i class="medium material-icons right">check_circle</i>{{=it.checkBtn.label}}
            </a>
        </div>
        {{?}}

    </div>
</div>
        