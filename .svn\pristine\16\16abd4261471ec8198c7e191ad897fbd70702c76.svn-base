.wizlet.GroupDirector .voteCountLabel.center > span {
  margin-right: 50%;
  -webkit-transform: translateX(50%);
      -ms-transform: translateX(50%);
       -o-transform: translateX(50%);
          transform: translateX(50%);
}
.wizlet.GroupDirector .voteCountLabel.fixed {
  position: fixed;
  right: 0;
  top: 50px;
  z-index: 999;
}
.wizlet.GroupDirector .voteCountLabel.fixed span {
  font-size: 100%;
  padding: 20px;
  line-height: 0;
  border-radius: 0 0 0 20px;
  opacity: 0.8;
}
.wizlet.GroupDirector .voteCountLabel.fixed span::after {
  font-size: 80%;
}
.wizlet.GroupDirector ul.tabs {
  overflow-x: auto;
  -ms-overflow-style: none;
}
.wizlet.GroupDirector ul.tabs::-webkit-scrollbar {
  height: 0 !important;
}
.wizlet.GroupDirector ul.tabs li.tab {
  width: auto;
  border-color: rgba(237, 7, 34, 0.2);
  border-style: solid;
  border-width: 0 1px 0 1px;
}
.wizlet.GroupDirector ul.tabs li.tab:first-child {
  border-left-width: 0;
}
.wizlet.GroupDirector ul.tabs li.tab.last {
  border-right-width: 0;
}
.wizlet.GroupDirector ul.tabs li.tab a {
  position: relative;
  padding: 0 5px;
  font-size: 1rem;
}
.wizlet.GroupDirector ul.tabs li.tab a.active, .wizlet.GroupDirector ul.tabs li.tab a:focus.active {
  background-color: #ed0722;
  color: white;
}
@media only screen and (min-width : 601px) {
  .wizlet.GroupDirector ul.tabs li.tab a {
    padding: 0 5px;
    font-size: 1.2rem;
  }
}
@media only screen and (min-width : 993px) {
  .wizlet.GroupDirector ul.tabs li.tab a {
    padding: 0 10px;
    font-size: 1.3rem;
  }
}
@media only screen and (min-width : 1201px) {
  .wizlet.GroupDirector ul.tabs li.tab a {
    font-size: 1.4rem;
  }
}
@media only screen and (min-width : 1401px) {
  .wizlet.GroupDirector ul.tabs li.tab a {
    font-size: 1.5rem;
  }
}
.wizlet.GroupDirector ul.tabs li.tab a i.check-icon {
  position: absolute;
  right: 15px;
}
.wizlet.GroupDirector .tabs-content.carousel.carousel-slider {
  height: auto !important;
}
.wizlet.GroupDirector .tabs-content.carousel.carousel-slider .carousel-item {
  overflow-y: auto;
  -ms-overflow-style: none;
}
.wizlet.GroupDirector .tabs-content.carousel.carousel-slider .carousel-item.active {
  position: relative;
}
.wizlet.GroupDirector .tabs-content.carousel.carousel-slider .carousel-item::-webkit-scrollbar {
  width: 0 !important;
}
.wizlet.GroupDirector .tabs-content.carousel.carousel-slider .carousel-item .collection .collection-item {
  cursor: pointer;
  display: inline-block;
  width: 100%;
  padding: 20px 10px;
  color: #333333;
}
.wizlet.GroupDirector .tabs-content.carousel.carousel-slider .carousel-item .collection .collection-item.active {
  color: #ffffff;
  background-color: #6d747e;
}
.wizlet.GroupDirector .tabs-content.carousel.carousel-slider .carousel-item .collection .collection-item.active.currentActionAction {
  background-color: #ed0722;
}
.wizlet.GroupDirector .tabs-content .container {
  margin: 0;
  width: auto;
}