﻿define([], function () {

    function cell(options) {        	
        
        options.dataBinder.context.off('updateModelInput').one('updateModelInput', function(e) {
            
            var value;
            var $context = $(options.dataBinder.context);
            
            if ($context.is("input") && !$context.is("input:text")) {
                //value = $context.attr('data-value').replace(/,/g, '');
                value = $context.attr('data-value');
            } else {
                value = $context.data('value');
            }
            console.log('updateModelInput', options.dataBinder.dataBinderOptions._attributes.source, value);
            change(options, value);
        });

    }
    
    function change(options, updatedValue) {
        var args = {};
        args.binder = options.dataBinder;
        args.bindString = options.dataBinder.dataBinderOptions._attributes.source;
        args.value = updatedValue;
        args.el = options.el;
        options.dataBinder.onViewItemChanged.call($(this), args);
    }

    return cell;
});


