/* 
    "WordCloudReport" Component to display 
*/

define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', 'd3', 'd3WordCloud', 'WodCloudUtil'], 
function ($, Q, WizerApi, WizletBase, doT, d3, d3WordCloud, WodCloudUtil) {

    var WordCloudReport = function () {
        this.type = 'WordCloudReport';
        this.level = 1;
    };
    var callResize = true;

    WordCloudReport.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.unsedContext = unused;
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;
        this.templateDefer = Q.defer();
        this.svgPromise = Q.defer();

        var self = this;
        var requirements = [];
        requirements.push(WizletBase.loadTemplate(wizletInfo, 'wordCloudreport.dot'));

        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        //requirements.push('js!./Wizer/Content/js/lib/d3.layout.cloud.js');
        
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });
        
        return WizletBase.loadHandler({ wizlet: this, render: this.render });
    };

    WordCloudReport.prototype.unloadHandler = function () {
        //unload wizletbase
        WizletBase.unloadHandler({ wizlet: this });
    };

    WordCloudReport.prototype.render = function (options) {
        var self = this;
          return self.templateDefer.promise.then(function (template) {
            var promise = self.getVotes();
            return promise.then(function (str) {
                self.wizletInfo.tags = [];
                var conf =self.initializeWordCloudReport(options.wizletInfo);
                self.parseText(str, conf);
                var fragment = template(options.wizletInfo);
                options.context.html(fragment);
               return true;
            });
        })
        .fail(this.wizerApi.showError)
    };   


    WordCloudReport.prototype.initializeWordCloudReport= function(wizletInfo){
                var conf = {};
                if (wizletInfo.maxWords) conf.maxWords = wizletInfo.maxWords;
                if (wizletInfo.colors) conf.colors = wizletInfo.colors;
                if (wizletInfo.font) conf.font = wizletInfo.font;
                if (wizletInfo.ignoreWords) conf.ignoreWords = wizletInfo.ignoreWords;
                return conf;
    }

    
    WordCloudReport.prototype.parseText = function (text, conf) {
        var self = this;
        //var stopWords = /^(the|i|me|my|myself|we|us|our|ours|ourselves|you|your|yours|yourself|yourselves|he|him|his|himself|she|her|hers|herself|it|its|itself|they|them|their|theirs|themselves|what|which|who|whom|whose|this|that|these|those|am|is|are|was|were|be|been|being|have|has|had|having|do|does|did|doing|will|would|should|can|could|ought|i'm|you're|he's|she's|it's|we're|they're|i've|you've|we've|they've|i'd|you'd|he'd|she'd|we'd|they'd|i'll|you'll|he'll|she'll|we'll|they'll|isn't|aren't|wasn't|weren't|hasn't|haven't|hadn't|doesn't|don't|didn't|won't|wouldn't|shan't|shouldn't|can't|cannot|couldn't|mustn't|let's|that's|who's|what's|here's|there's|when's|where's|why's|how's|a|an|the|and|but|if|or|because|as|until|while|of|at|by|for|with|about|against|between|into|through|during|before|after|above|below|to|from|up|upon|down|in|out|on|off|over|under|again|further|then|once|here|there|when|where|why|how|all|any|both|each|few|more|most|other|some|such|no|nor|not|only|own|same|so|than|too|very|say|says|said|shall)$/,
            var stopWords = new RegExp('^(' +conf.ignoreWords + ')$'),
            punctuation = new RegExp("[" + unicodePunctuationRe + "]", "g"),
            wordSeparators = /[\s\u3031-\u3035\u309b\u309c\u30a0\u30fc\uff70]+/g,
            discard = /^(@|https?:|\/\/)/;
        self.tags = {};
        var cases = {};
        text.split(wordSeparators).forEach(function (word) {
            if (discard.test(word)) return;
            word = word.replace(punctuation, "");
            if (stopWords.test(word.toLowerCase())) return;
            word = word.substr(0, self.maxLength);
            cases[word.toLowerCase()] = word;
            self.tags[word = word.toLowerCase()] = (self.tags[word] || 0) + 1;
        });

        self.tags = d3.entries(self.tags).sort(function (a, b) { return b.value - a.value; });
        self.tags.forEach(function (d) { d.key = cases[d.key]; });
        if(conf.maxWords < self.tags.length) self.tags = self.tags.slice(0, conf.maxWords)
        self.wizletInfo.tags = self.tags;
        
        var min = d3.min(self.tags, function(d){ return d.value });
        var max = d3.max(self.tags, function(d){ return d.value });
        
        // console.log(min, max);
        
        var myScale = d3.scale[self.wizletInfo.scaleType]()
        .domain([min, max])
        .range([30, 60]);

        self.tags.forEach(function (d) { d.size = parseInt(myScale(d.value)) });
        
        // console.log(self.tags);
        
        // self.generate();
    }
    WordCloudReport.prototype.getVotes = function () {
        var self = this;
        var defer = Q.defer();
        var qId=[];
        self.wizletInfo.questions.forEach(function(question,index){
            qId.push(self.wizerApi.getQuestionIdByName(self.wizletInfo.questions[index].binding));
        });
        self.trackQuestionId = self.wizerApi.getQuestionIdByName(self.wizletInfo.trackQuestion);
        self.wizerApi.getVotes({ questionIds: qId, groupQuestionId: self.trackQuestionId }).then(function (result) {
            var str = "";
            result.votes.forEach(function (vote) {
                str += (str) ? ", " + vote.responseText : vote.responseText;
            });
            defer.resolve(str);
        });        
        return defer.promise;
    }

    WordCloudReport.getRegistration = function () {
        return new WordCloudReport();
    };

    return WordCloudReport;

});
